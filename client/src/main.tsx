import React from "react";
import ReactDOM from "react-dom/client";
import { createTheme, MantineProvider } from '@mantine/core';
import App from "./App";
import 'uno.css'
import '@unocss/reset/tailwind.css'; // 或者使用normalize.css
import '@mantine/core/styles.css';
import '@mantine/notifications/styles.css';
import '@mantine/dropzone/styles.css';
import '@vidstack/react/player/styles/default/theme.css';
import '@vidstack/react/player/styles/default/layouts/video.css';
import "yet-another-react-lightbox/styles.css";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Notifications } from '@mantine/notifications'
import { ThemeProvider } from "./common/contexts";
import { ChatProvider } from "./agents/contexts";
import * as Sentry from "@sentry/react";
if (import.meta.env.PROD) {
  Sentry.init({
    dsn: "https://<EMAIL>/2",
    integrations: [],
    tracesSampleRate: 0,
  });
}

const theme = createTheme({
});

// 创建 QueryClient 实例
const queryClient = new QueryClient();

// 添加媒体权限请求
if ('mediaDevices' in navigator) {
  navigator.mediaDevices.getUserMedia({ audio: true })
    .catch(() => console.warn('用户拒绝了麦克风权限'));
}

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <MantineProvider defaultColorScheme="auto" theme={theme}>
        <Notifications position="top-center" />
        <ThemeProvider>
          <ChatProvider>
            <App />
          </ChatProvider>
        </ThemeProvider>
      </MantineProvider>
    </QueryClientProvider>
  </React.StrictMode>,
);
