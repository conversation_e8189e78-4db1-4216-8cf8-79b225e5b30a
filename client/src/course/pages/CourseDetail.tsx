import {
    Badge,
    Box,
    Container,
    Grid,
    Group,
    Image,
    Skeleton,
    Stack,
    Tabs,
    Text,
    ThemeIcon,
    useMantineTheme
} from '@mantine/core';
import {useQuery} from '@tanstack/react-query';
import {MediaPlayer, MediaProvider} from '@vidstack/react';
import {defaultLayoutIcons, DefaultVideoLayout} from '@vidstack/react/player/layouts/default';
import {motion} from 'motion/react';
import {useEffect, useState} from 'react';
import {MdOndemandVideo} from 'react-icons/md';
import {RiTimeLine} from 'react-icons/ri';
import {useParams} from 'react-router-dom';
import {getCourseDetail} from '../api';
import {CoursePricingCard} from '../components';
import {useCourseProgressStore} from '../stores';
import {CourseSectionOutline} from '../types';
import {useTheme} from '@/common/contexts';

const CourseDetail = () => {
  const { id } = useParams<{ id: string }>();

  const { data: course, isLoading, error } = useQuery({
    queryKey: ['courseDetail', id],
    queryFn: async () => {
      const response = await getCourseDetail(Number(id));
      return response.data;
    },
    enabled: !!id,
  });

  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();

  const [currentSection, setCurrentSection] = useState<CourseSectionOutline | undefined>(
    course?.sections[0]
  );

  const { updateProgress, getCourseProgress, completeSection } = useCourseProgressStore();

  const [lastSavedTime, setLastSavedTime] = useState(0);

  useEffect(() => {
    if (!course) return;
    setCurrentSection(course.sections[0]);
  }, [course]);

  if (isLoading) return (
    <div style={{ height: '100%', overflowY: 'auto' }}>
      <Container size="xl" py="xl">
        <Stack gap="xl">
          <Skeleton
            height={500}
            radius="md"
            style={{ aspectRatio: '16/9', backgroundColor: theme.colors.dark[8] }}
          />

          <Grid gutter="xl">
            <Grid.Col span={8}>
              <Stack gap="md">
                <Skeleton height={40} width="60%" radius="md" />

                <Group gap="xs">
                  {[1,2,3].map(i => (
                    <Skeleton key={i} height={32} width={80} radius="xl" />
                  ))}
                </Group>

                <Skeleton height={80} radius="md" />
              </Stack>
            </Grid.Col>

            <Grid.Col span={4}>
              <Skeleton
                height={300}
                radius="md"
                style={{
                  border: `1px solid ${theme.colors.gray[7]}`,
                  padding: 16
                }}
              />
            </Grid.Col>
          </Grid>

          <Tabs defaultValue="sections">
            <Tabs.Panel value="sections" pt="md">
              <Stack gap="xs">
                {[1,2,3,4,5].map(i => (
                  <Skeleton
                    key={i}
                    height={80}
                    radius="md"
                    style={{ border: `1px solid ${theme.colors.gray[7]}` }}
                  />
                ))}
              </Stack>
            </Tabs.Panel>
          </Tabs>
        </Stack>
      </Container>
    </div>
  );

  if (error) return <Text c="red">加载失败: {(error as Error).message}</Text>;
  if (!course) return <Text>未找到课程</Text>;

  return (
    <div style={{ height: '100%', overflowY: 'auto' }}>
      <Container size="xl" py="xl"  >
        <Stack>
          <Group align="flex-start" gap={30}>
            {(course.has_purchased || currentSection?.is_free) ? (
              <Box style={{
                position: 'relative',
                overflow: 'hidden',
                maxWidth: '782px',
                borderRadius: theme.radius.md,
                aspectRatio: '16/9',
                backgroundColor: theme.colors.dark[8]
              }}>
                <MediaPlayer
                  title={course.title}
                  aspectRatio="16/9"
                  src={currentSection?.video_url}
                  style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: theme.radius.md
                  }}
                  onTimeUpdate={({ currentTime }) => {
                    if (currentSection?.id) {
                      const now = Date.now();
                      if (now - lastSavedTime > 5000 || currentTime === currentSection.duration) {
                        updateProgress(course.id, {
                          sectionProgress: {
                            [currentSection.id]: currentTime
                          }
                        });
                        setLastSavedTime(now);
                      }
                    }
                  }}
                  onEnded={() => {
                    if (currentSection?.id) {
                      completeSection(course.id, currentSection.id);
                    }
                  }}
                >
                  <MediaProvider />
                  <DefaultVideoLayout icons={defaultLayoutIcons} />
                </MediaPlayer>
              </Box>

            ) : (
              <Box style={{
                position: 'relative',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden',
                maxWidth: '510px',
                borderRadius: 10,
                aspectRatio: '4/2.7',
                backgroundColor: theme.colors.dark[8]
              }}>
                <Image
                  fit="cover"
                  src={course.cover_image}
                  alt={course.title}
                />
              </Box>
            )}

            {(course.has_purchased || currentSection?.is_free) ? (
              <Stack
                p="16px 20px"
                align='center'
                gap={12}
                styles={{
                  root: {
                    width: '230px',
                    backgroundColor: actualColorScheme === 'dark' ? theme.colors.dark[6] : theme.colors.gray[0],
                    borderRadius: 10,
                  }
                }}
              >
                <Text
                  align="left"
                  w="100%"
                  lh="18px"
                  fz={12}
                  fw={500}
                  c={actualColorScheme === 'dark' ? theme.colors.gray[3] : theme.colors.dark[6]}
                >
                  班主任【某某某老师】微信
                </Text>

                <Stack
                  gap={10}
                  styles={{
                    root: {
                      width: '100%',
                      padding: '16px',
                      border: `1px solid ${actualColorScheme === 'dark' ? theme.colors.dark[4] : theme.colors.gray[2]}`,
                    }
                  }}
                >
                  <Group gap={5}>
                    <Image
                      src="https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/images/bg-7.png"
                      fit="cover"
                      style={{
                        width: '36px',
                        height: '36px',
                        borderRadius: '4px',
                      }}
                    />
                    <Stack
                      gap={4}
                    >
                      <Text
                        lh="14px"
                        fz={10}
                        fw={500}
                        c={actualColorScheme === 'dark' ? theme.colors.gray[3] : theme.colors.dark[6]}
                      >
                        王小样
                      </Text>
                      <Text
                        lh="12px"
                        fz={8}
                        c={actualColorScheme === 'dark' ? theme.colors.gray[5] : theme.colors.gray[6]}
                      >
                        WEB前端工程师
                      </Text>
                    </Stack>
                  </Group>

                  <Image
                    src={null}
                    fit="cover"
                    fallbackSrc="https://placehold.co/600x600?text=Placeholder"
                    style={{
                      width: '144px',
                      height: '144px',
                      margin: '0 auto',
                    }}
                  />
                  <Text
                    align="center"
                    lh="12px"
                    fz={8}
                    c={actualColorScheme === 'dark' ? theme.colors.gray[5] : theme.colors.gray[6]}
                  >
                    扫一扫上面的二维码，加老师微信
                  </Text>
                </Stack>

                <Text
                  align="center"
                  lh="18px"
                  fz={12}
                  fw={500}
                  c={actualColorScheme === 'dark' ? theme.colors.gray[3] : theme.colors.dark[6]}
                >
                  微信号: weixinmingzi
                  <br />
                  联系电话: 15800000000
                </Text>
                <Text
                  lh="15px"
                  fz={10}
                  fw={400}
                  c={actualColorScheme === 'dark' ? theme.colors.gray[5] : theme.colors.gray[6]}
                >
                  如有疑问，请联系班主任老师咨询
                </Text>
              </Stack>
            ) : (
              <CoursePricingCard
                course={course}
                onStartLearning={() => {
                  const progress = getCourseProgress(course.id);
                  let targetSection = course.sections[0];

                  if (progress?.sectionProgress) {
                    const sectionsWithProgress = course.sections.filter(section =>
                      section.id in progress.sectionProgress
                    );

                    if (sectionsWithProgress.length > 0) {
                      targetSection = sectionsWithProgress.reduce((prev, current) =>
                        current.id > prev.id ? current : prev
                      );
                    }
                  }

                  setCurrentSection(targetSection);
                }}
              />
            )}
          </Group>

          <Stack gap="md">
            <Text size="xl" fw={700} c={actualColorScheme === 'dark' ? theme.colors.gray[1] : theme.colors.dark[6]}>{course.title}</Text>
            <Group gap="xs">
              {course.tags.map(tag => (
                <Badge key={tag} variant="light">
                  {tag}
                </Badge>
              ))}
            </Group>
            <Text
              lh="28px"
              fz={16}
              fw={500}
              c={actualColorScheme === 'dark' ? theme.colors.gray[5] : theme.colors.gray[6]}
            >
              {course.description}
            </Text>
          </Stack>
          <Tabs
            defaultValue="detail"
            mt="xl"
            styles={{
              root: {
                width: '782px',
              },
            }}
          >
            <Tabs.List>
              <Tabs.Tab
                value="detail"
                color="#326BFF"
              >
                <Text
                  lh="30px"
                  fz={20}
                  fw={700}
                  c={actualColorScheme === 'dark' ? theme.colors.gray[3] : theme.colors.dark[6]}
                >
                  课程详情
                </Text>
              </Tabs.Tab>
              <Tabs.Tab
                value="sections"
                color="#326BFF"
              >
                <Text
                  lh="30px"
                  fz={20}
                  fw={700}
                  c={actualColorScheme === 'dark' ? theme.colors.gray[3] : theme.colors.dark[6]}
                >
                  课程列表
                </Text>
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="detail" pt="md">
              <Stack gap="md">
                {course.poster_url && (
                  <Image
                    src={course.poster_url}
                    alt="课程海报"
                    radius="md"
                    mb="md"
                  />
                )}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="sections" pt="md">
              <Stack gap="xs">
                {course.sections.map((section) => (
                  <motion.div
                    key={section.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    transition={{
                      type: "spring",
                      stiffness: 150,
                      damping: 20,
                      mass: 0.5
                    }}
                  >
                    <Group
                      p="md"
                      style={{
                        position: 'relative',
                        backgroundColor: actualColorScheme === 'dark' ? theme.colors.dark[6] : theme.colors.gray[0],
                        borderRadius: '12px',
                        cursor: 'pointer',
                        overflow: 'hidden',
                      }}
                      onClick={() => {
                        if (section.is_free || course.has_purchased) {
                          setCurrentSection(section);
                          updateProgress(course.id, {
                            sectionProgress: {
                              [section.id]: 0
                            }
                          });
                        }
                      }}
                    >
                      <motion.div
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          background: theme.colors.blue[0],
                          opacity: 0,
                        }}
                        animate={{ opacity: [0, 0.08, 0] }}
                        transition={{ duration: 0.8 }}
                      />

                      <ThemeIcon
                        color={section.is_free ? 'teal' : 'blue'}
                        size={40}
                        radius="12px"
                        variant="light"
                      >
                        <MdOndemandVideo size="1.2rem" />
                      </ThemeIcon>

                      <Stack gap={4} style={{ flex: 1 }}>
                        <Group justify="space-between">
                          <Text
                            lh="18px"
                            fz={14}
                            fw={600}
                            c={actualColorScheme === 'dark' ? theme.colors.gray[3] : theme.colors.dark[6]}
                          >
                            {section.title}
                          </Text>
                          {section.is_free && (
                            <Badge variant="light" color="teal" radius="sm">
                              免费试看
                            </Badge>
                          )}
                        </Group>
                        <Group c={theme.colors.gray[6]} gap={4}>
                          <RiTimeLine size="14px" />
                          <Text
                            lh="14px"
                            fz={10}
                            fw={500}
                          >
                            {Math.floor(section.duration / 60)}分钟
                          </Text>
                        </Group>
                      </Stack>
                    </Group>
                  </motion.div>
                ))}
              </Stack>
            </Tabs.Panel>
          </Tabs>

        </Stack>

      </Container>
    </div>
  );
};

export default CourseDetail;
