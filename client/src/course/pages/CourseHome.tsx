import MarketplaceHeader from '@/agents/components/MarketplaceHeader';
import {Container, Grid, Group, Pagination, Skeleton, Stack, Text} from '@mantine/core';
import {useQuery} from '@tanstack/react-query';
import {useState} from 'react';
import {searchCourses} from '../api';
import {CourseCard, CourseTagFilter} from '../components';
import {Pagination as PaginationType, ResponsePayloads} from '@/common';
import {CourseOutline} from '../types';

const CourseHome = () => {
  const [searchParams, setSearchParams] = useState({
    keyword: '',
    tags: [] as string[],
    page: 1,
    page_size: 12
  });

  // 修改筛选条件处理
  const handleSearch = (value: string) => {
    setSearchParams(prev => ({ ...prev, keyword: value, page: 1 }));
  };

  const handleTagFilter = (tags: string[]) => {
    setSearchParams(prev => ({ ...prev, tags, page: 1 }));
  };

  // 请求参数调整
  const { data, isLoading, error } = useQuery<ResponsePayloads<PaginationType<CourseOutline>>>({
    queryKey: ['courses', searchParams],
    queryFn: () => searchCourses(searchParams),
  });

  return (
    <div style={{ height: '100%', overflowY: 'auto' }}>
      <MarketplaceHeader onSearch={handleSearch} placeholder="搜索课程..." />
      <Container mt={32} size="xl" py="md"  >
        <Stack gap={24}>
          <CourseTagFilter value={searchParams.tags} onChange={handleTagFilter} />

          {/* 课程列表 */}
          {isLoading ? (
            <Grid>
              {[...Array(8)].map((_, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, lg: 3 }}>
                  <Skeleton
                    height={300}
                    radius="xl"
                    style={{
                      borderRadius: '24px',
                      overflow: 'hidden'
                    }}
                  />
                  <Stack mt="sm" gap={8} p={16}>
                    <Skeleton height={20} width="80%" radius="md" />
                    <Skeleton height={16} width="60%" radius="md" />
                    <Skeleton height={24} width="50%" radius="md" mt={8} />
                  </Stack>
                </Grid.Col>
              ))}
            </Grid>
          ) : error ? (
            <Text c="red">加载失败: {(error as Error).message}</Text>
          ) : (
            <>
              <Grid>
                {data?.data?.data?.map((course) => (
                  <Grid.Col key={course.id} span={{ base: 12, sm: 6, lg: 3 }}>
                    <CourseCard course={course} />
                  </Grid.Col>
                ))}
              </Grid>
              {/* 分页器 */}
              <Group justify="center">
                <Pagination
                  color='#326BFF'
                  value={searchParams.page}
                  onChange={(value) => setSearchParams(prev => ({ ...prev, page: value, page_size: 9 }))}
                  total={Math.ceil((data?.data?.total || 0) / searchParams.page_size)}
                />
              </Group>
            </>
          )}
        </Stack>
      </Container>
    </div>
  );
};

export default CourseHome;
