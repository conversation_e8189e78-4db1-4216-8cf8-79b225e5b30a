export interface CourseOutline {
  /** 课程ID */
  id: number;
  /** 课程标题 */
  title: string;
  /** 课程封面图片URL */
  cover_image?: string;
  /** 课程海报图片URL */
  poster_url?: string;
  /** 课程标签列表 */
  tags?: string[];
  /** 课程价格 */
  price: number;
  /** 课程章节数量 */
  sections_count: number;
  /** 课程讲师 */
  instructor: string;
}

export interface CourseSectionOutline {
  /** 章节ID */
  id: number;
  /** 章节标题 */
  title: string;
  /** 章节时长（秒） */
  duration: number;
  /** 是否免费 */
  is_free: boolean;
  /** 章节视频地址 */
  video_url: string;
}

export interface CourseDetail {
  /** 课程ID */
  id: number;
  /** 课程标题 */
  title: string;
  /** 课程描述 */
  description: string;
  /** 课程标签列表 */
  tags: string[];
  /** 课程章节列表 */
  sections: CourseSectionOutline[];
  /** 课程封面图片URL */
  cover_image?: string;
  /** 课程海报图片URL */
  poster_url?: string;
  /** 课程价格 */
  price: number;
  /** 课程讲师 */
  instructor: string;
  /** 是否已购买 */
  has_purchased: boolean;
}
export interface SearchCoursesParams {
  keyword?: string;
  tags?: string[];
  page?: number;
  page_size?: number;
  teacher_id?: string;
  exclude_ids?: string[];
}
