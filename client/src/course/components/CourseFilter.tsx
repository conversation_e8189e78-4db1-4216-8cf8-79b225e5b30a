import {Button, Group, Skeleton, Text} from '@mantine/core';
import {useQuery} from '@tanstack/react-query';
import {getCourseTags} from '../api';

export type CourseFilterType = string;

interface CourseFilterProps {
  value?: string[];
  onChange?: (value: string[]) => void;
}

const CourseFilter: React.FC<CourseFilterProps> = ({ value = [], onChange }) => {
  const { data: tags, isLoading, isError } = useQuery({
    queryKey: ['courseTags'],
    queryFn: () => getCourseTags().then(res => ['全部', ...(res.data ?? [])]),
  });

  const handleFilterClick = (filter: string) => {
    const newFilters = value.includes(filter)
      ? value.filter(f => f !== filter)
      : [...value, filter];

    // 处理"全部"选项的互斥逻辑
    if (filter === '全部') {
      onChange?.(newFilters.includes('全部') ? ['全部'] : []);
    } else {
      onChange?.(newFilters.filter(f => f !== '全部'));
    }
  };

  if (isLoading) return <Skeleton height={40} />;
  if (isError) return <Text c="red">加载标签失败</Text>;

  return (
    <Group gap="md">
      {tags?.map((tag) => (
        <Button
          key={tag}
          variant={value.includes(tag) ? 'filled' : 'subtle'}
          color={value.includes(tag) ? 'blue' : 'gray'}
          onClick={() => handleFilterClick(tag)}
          styles={{
            root: {
              fontWeight: 'normal',
              padding: '6px 16px',
              height: 'auto',
            },
            label: {
              fontSize: '14px',
            },
          }}
        >
          {tag}
        </Button>
      ))}
    </Group>
  );
};

export default CourseFilter;
