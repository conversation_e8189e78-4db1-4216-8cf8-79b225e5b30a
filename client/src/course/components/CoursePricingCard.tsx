import {Button, Group, Modal, Stack, Text, Title, useMantineTheme} from '@mantine/core';
import {CourseDetail} from '../types';
import {useMutation} from '@tanstack/react-query';
import {createOrder} from '@/user/api';
import {useState} from 'react';
import {notifications} from '@mantine/notifications';
import {QRCodeSVG} from 'qrcode.react';
import {FaAlipay, FaWeixin} from 'react-icons/fa';
import {useCourseProgressStore} from '../stores';
import {useTheme} from '@/common/contexts';

interface CoursePricingCardProps {
  course: CourseDetail;
  onEnroll?: () => void;
  style?: React.CSSProperties;
  onStartLearning?: () => void;
}

const CoursePricingCard: React.FC<CoursePricingCardProps> = ({ course, onEnroll, style, onStartLearning }) => {
  const [paymentMethod, setPaymentMethod] = useState<'alipay' | 'wechatpay' | null>(null);
  const [wechatPaymentUrl, setWechatPaymentUrl] = useState<string | null>(null);
  const { getCourseProgress } = useCourseProgressStore();
  const progress = getCourseProgress(course.id);

  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();

  // 支付处理逻辑
  const handlePayment = useMutation({
    mutationFn: createOrder,
    onSuccess: (data) => {
      if (data.data) {
        notifications.show({
          title: '提示',
          message: '订单已创建，正在跳转...',
          color: 'green'
        });
        if (data.data?.payment_url) {
          if (paymentMethod === 'wechatpay') {
            setWechatPaymentUrl(data.data.payment_url);
          } else {
            window.open(data.data.payment_url, '_blank');
          }
        }
        onEnroll?.();
      } else {
        notifications.show({
          title: '支付失败',
          message: data.error?.message || '未知错误',
          color: 'red'
        });
      }
    },
    onError: (error) => {
      notifications.show({
        title: '请求失败',
        message: error.message,
        color: 'red'
      });
    }
  });

  return (
    <Stack className="relative flex-auto" gap={0} style={{
      height: '345px',
    }}>
      <Title order={2}lineClamp={2} c={actualColorScheme === 'dark' ? theme.colors.gray[1] : theme.colors.dark[6]}>{course.title}</Title>
      <Group mt={8} gap={25}>
        <Text
          fz={12}
          fw={500}
          c={'#999999'}
        >
          授课老师：{course.instructor}
        </Text>
        <Text
          fz={12}
          fw={500}
          c={'#999999'}
        >
          开课时间：2020-02-22至2020-04-22
        </Text>
      </Group>
      <Group
        p="0 25px"
        mt={8}
        gap={40}
        style={{
          width: '390px',
          height: '75px',
          marginLeft: '5px',
          backgroundColor: actualColorScheme === 'dark' ? theme.colors.dark[6] : theme.colors.gray[0],
        }}
      >
        <Stack
          align='center'
          justify='center'
          gap={5}
        >
          <Text
            lh="20px"
            fz={16}
            fw={700}
            c={actualColorScheme === 'dark' ? theme.colors.gray[3] : theme.colors.dark[4]}
          >
            {course.sections.length}
          </Text>
          <Text
            lh="18px"
            fz={12}
            fw={400}
            c={'#999999'}
          >
            课程数量
          </Text>
        </Stack>
        <Stack
          align='center'
          justify='center'
          gap={5}
        >
          <Text
            lh="20px"
            fz={16}
            fw={700}
            c={actualColorScheme === 'dark' ? theme.colors.gray[3] : theme.colors.dark[4]}
          >
            {course.sections.length}
          </Text>
          <Text
            lh="18px"
            fz={12}
            fw={400}
            c={'#999999'}
          >
            课程数量
          </Text>
        </Stack>
      </Group>

      <Stack
        className="absolute bottom-0px flex-auto w-400px"
        gap={24}
      >
        {/* 价格 */}
        {course.has_purchased ? null : (
          <Group align='flex-end' gap={0}>
            <Text
              lh="18px"
              fz={12}
              fw={500}
              c={'#999999'}
              span
            >
              价格：
            </Text>
            <Text
              lh="16px"
              fz={12}
              fw={500}
              c={'#E33B3B'}
              span
            >
              ¥
            </Text>
            <Text
              lh="24px"
              fz={22}
              fw={500}
              c={'#E33B3B'}
              span
            >
              {course.price}
            </Text>
          </Group>
        )}

        {/* 支付按钮 */}
        {course.has_purchased ? (
          <Button
            fullWidth
            color="green"
            size="md"
            onClick={onStartLearning}
          >
            {progress?.sectionProgress && Object.keys(progress.sectionProgress).length > 0
              ? "继续学习"
              : "开始学习"}
          </Button>
        ) : (
          <Stack gap="sm">
            <Button
              fullWidth
              variant="filled"
              color="#FF7A3E"
              size="md"
              onClick={() => {
                if (!paymentMethod) {
                  notifications.show({
                    title: '请选择支付方式',
                    message: '请先选择支付宝或微信支付',
                    color: 'yellow'
                  });
                  return;
                }
                handlePayment.mutate({
                  items: [{
                    asset_type: 'course',
                    asset_id: course.id.toString(),
                    quantity: 1,
                    unit_price: course.price.toString()
                  }],
                  payment_method: paymentMethod,
                });
              }}
              loading={handlePayment.isPending}
            >
              立即购买
            </Button>

            <Group gap="xs">
              <Button
                variant={paymentMethod === 'alipay' ? 'filled' : 'outline'}
                color="blue"
                style={{ flex: 1 }}
                leftSection={<FaAlipay size={20} />}
                onClick={() => setPaymentMethod('alipay')}
              >
                支付宝
              </Button>
              <Button
                variant={paymentMethod === 'wechatpay' ? 'filled' : 'outline'}
                color="green"
                style={{ flex: 1 }}
                leftSection={<FaWeixin size={20} />}
                onClick={() => setPaymentMethod('wechatpay')}
              >
                微信支付
              </Button>
            </Group>
          </Stack>
        )}
      </Stack>

      {/* 添加微信支付弹窗 */}
      <Modal
        opened={!!wechatPaymentUrl}
        onClose={() => setWechatPaymentUrl(null)}
        title="微信扫码支付"
        centered
      >
        <Group justify="center" p="md">
          {wechatPaymentUrl && (
            <>
              <QRCodeSVG
                value={wechatPaymentUrl}
                size={256}
                level="H"
                bgColor="#ffffff"
                fgColor="#07c160"
                title="微信支付二维码"
                marginSize={4}
              />
              <Text c="dimmed" size="sm" mt="md">
                请使用微信扫描二维码完成支付
              </Text>
            </>
          )}
        </Group>
      </Modal>
    </Stack>
  );
};

export default CoursePricingCard;
