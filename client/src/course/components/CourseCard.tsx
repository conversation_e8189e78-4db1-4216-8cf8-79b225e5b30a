/** @jsxImportSource @emotion/react */
import {css} from '@emotion/react';
import {useTheme} from '@/common/contexts';
import {Card, Group, Image, Stack, Text, Title, useMantineTheme} from '@mantine/core';
import {Link} from 'react-router-dom';
import {CourseOutline} from '../types';

interface CourseCardProps {
  course: CourseOutline;
  onEnroll?: () => void;
}

const CourseCard: React.FC<CourseCardProps> = ({ course, onEnroll }) => {
  const { id, title, cover_image, price } = course;
  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();

  return (
    <Link to={`/course/${course.id}`} style={{ textDecoration: 'none', color: 'inherit' }}>
      <Card
        shadow="none"
        padding={8}
        mb={8}
        radius={4}
        withBorder={false}
        css={css`
            &:hover{
              box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.08);
              transform: scale(1.02);
            }
          `}
          styles={{
            root: {
              backgroundColor: actualColorScheme === 'dark' ? '#2D333B' : 'white',
              borderColor: actualColorScheme === 'dark' ? theme.colors.gray[8] : theme.colors.gray[1],
              transition: 'transform 0.2s',
            },
            section: {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              overflow: 'hidden',
              borderRadius: 4,
              aspectRatio: '4/2.7',
            }
          }}
      >
        <Card.Section>
          <Image
            src={cover_image}
            fit="cover"
            alt={title}
            style={{
              transform: 'translateY(20px)',
            }}
          />
        </Card.Section>

        <Stack mt={10} gap={8}>
          <Title h={40} lh="20px" fz={14} fw={500} c={actualColorScheme === 'dark' ? '#E8F2FF' : '#545454'} order={3} lineClamp={2}>
            {title}
          </Title>

          <Group justify="space-between" align="center">
            <Group py={4} justify="flex-start" align="flex-end" gap={4}>
              <Text
                fz={16}
                lh="15px"
                fw={600}
                c={actualColorScheme === 'dark'? '#FF6B6B' : '#FF5024'}
                style={{
                  letterSpacing: '-0.03em'
                }}
              >
                ¥ {price}
              </Text>
              <Text
                fz={12}
                lh="12px"
                c="dimmed"
                style={{
                  textDecoration: 'line-through',
                  opacity: 0.7
                }}
              >
                ¥{Math.round(price * 1.5)}
              </Text>
            </Group>
            <Text
              lh="18px"
              fz={12}
              fw={500}
              c="dimmed"
            >
              多人参与
            </Text>
          </Group>

        </Stack>
      </Card>
    </Link>
  );
};

export default CourseCard;
