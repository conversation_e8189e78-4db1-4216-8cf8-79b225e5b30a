/** @jsxImportSource @emotion/react */
import {useTheme} from '@/common/contexts';
import {css} from '@emotion/react';
import {ResponsePayloads} from '@/common';
import {Chip, Group, Skeleton, useMantineTheme} from '@mantine/core';
import {useQuery} from '@tanstack/react-query';
import {getCourseTags} from '../api';

interface CourseTagFilterProps {
  value: string[];
  onChange: (tags: string[]) => void;
}

const CourseTagFilter = ({ value, onChange }: CourseTagFilterProps) => {
  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();

  const { data, isLoading } = useQuery<ResponsePayloads<string[]>>({
    queryKey: ['courseTags'],
    queryFn: getCourseTags
  });

  if (isLoading) {
    return <Skeleton height={40} width={240} radius="md" />;
  }

  // 根据主题设置不同的颜色变量
  const isDark = actualColorScheme === 'dark';

  // 默认底色和文字颜色
  const defaultBgColor = isDark ? theme.colors.dark[6] : theme.colors.gray[1];
  const defaultTextColor = isDark ? theme.colors.gray[3] : theme.colors.dark[5];

  // 选中时的底色和文字颜色
  const selectedBgColor = 'linear-gradient(90deg, rgba(86, 104, 255, 1) 0%, rgba(36, 162, 254, 1) 100%), rgba(50, 107, 255, 1)';
  const selectedTextColor = '#ffffff';

  return (
    <Chip.Group multiple value={value} onChange={onChange}>
      <Group gap="md">
        {data?.data?.map(tag => (
          <Chip
            key={tag}
            value={tag}
            css={css`
              & label {
                background-color: ${defaultBgColor} !important;
                border: 0 !important;
                color: ${defaultTextColor} !important;
              }

              & label[data-checked] {
                background: ${selectedBgColor} !important;
                color: ${selectedTextColor} !important;
              }
            `}
            styles={{
              root: {
                '--chip-fz': '14px',
                '--chip-size': '40px',
                '--chip-padding': '13px',
                '--chip-checked-padding': '13px',
                '--chip-radius': '10px',
                transition: 'all 0.2s ease'
              },
              label: {
                fontWeight: '500',
              },
              input: {
                display: 'none'
              },
              iconWrapper: {
                display: 'none'
              }
            }}
          >
            {tag}
          </Chip>
        ))}
      </Group>
    </Chip.Group>
  );
};

export default CourseTagFilter;
