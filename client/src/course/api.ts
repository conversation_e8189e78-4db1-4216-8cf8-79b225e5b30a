import { API_BASE_URL, Pagination, ResponsePayloads } from '@/common';
import { request } from '@/common/utils';
import { useUserStore } from '@/user/stores';
import { CourseDetail, CourseOutline, SearchCoursesParams } from './types';

export async function searchCourses(params: SearchCoursesParams): Promise<ResponsePayloads<Pagination<CourseOutline>>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const queryParams = new URLSearchParams();
  if (params.keyword) queryParams.append('keyword', params.keyword);
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.page_size) queryParams.append('page_size', params.page_size.toString());
  if (params.teacher_id) queryParams.append('teacher_id', params.teacher_id);

  // 处理标签 - 与智能体搜索保持一致，每个标签单独添加
  if (params.tags && params.tags.length > 0) {
    params.tags.forEach((tag) => queryParams.append('tags', tag));
  }

  // 处理排除的ID - 与智能体搜索保持一致，每个ID单独添加
  if (params.exclude_ids && params.exclude_ids.length > 0) {
    params.exclude_ids.forEach((id) => queryParams.append('exclude_ids', id));
  }

  const response = await request(`${API_BASE_URL}/courses/search?${queryParams.toString()}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`搜索课程失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取课程标签
 * @returns 课程标签
 */
export async function getCourseTags(): Promise<ResponsePayloads<string[]>> {
  const response = await request(`${API_BASE_URL}/courses/tags`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${useUserStore.getState().token}`,
    },
  });

  if (!response.ok) {
    throw new Error('获取标签失败');
  }

  return response.json();
}

/**
 * 获取课程详情
 * @param id 课程ID
 * @returns 课程详情
 */
export async function getCourseDetail(id: number): Promise<ResponsePayloads<CourseDetail>> {
  const token = useUserStore.getState().token;

  const response = await request(`${API_BASE_URL}/courses/details/${id}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取课程详情失败: ${response.statusText}`);
  }

  return response.json();
}
