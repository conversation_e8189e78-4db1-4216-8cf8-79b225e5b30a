import {create} from 'zustand';
import {createJSONStorage, devtools, persist} from 'zustand/middleware';

export interface CourseProgress {
    /** 课程ID */
    courseId: number;
    /** 各章节播放进度（秒） */
    sectionProgress: Record<number, number>;
}

interface CourseProgressState {
    /** 所有课程的学习进度 */
    progresses: CourseProgress[];

    /**
     * 更新或创建课程学习进度
     * @param courseId 课程ID
     * @param progress 进度信息
     */
    updateProgress: (courseId: number, progress: Partial<CourseProgress>) => void;

    /**
     * 标记章节完成
     * @param courseId 课程ID
     * @param sectionId 章节ID
     */
    completeSection: (courseId: number, sectionId: number) => void;

    /**
     * 获取指定课程的学习进度
     * @param courseId 课程ID
     */
    getCourseProgress: (courseId: number) => CourseProgress | undefined;

    /**
     * 重置课程进度
     * @param courseId 课程ID
     */
    resetProgress: (courseId: number, sectionId?: number) => void;
}

export const useCourseProgressStore = create<CourseProgressState>()(
    devtools(
        persist(
            (set, get) => ({
                progresses: [],

                updateProgress: (courseId, progress) => set((state) => {
                    const existing = state.progresses.find(p => p.courseId === courseId);
                    if (existing) {
                        return {
                            progresses: state.progresses.map(p =>
                                p.courseId === courseId ? {
                                    ...p,
                                    sectionProgress: {
                                        ...p.sectionProgress,
                                        ...progress.sectionProgress
                                    }
                                } : p
                            )
                        };
                    }
                    return {
                        progresses: [...state.progresses, {
                            courseId,
                            sectionProgress: progress.sectionProgress || {},
                        }]
                    };
                }, false, "updateProgress"),

                completeSection: (courseId, sectionId) => set((state) => ({
                    progresses: state.progresses.map(p => {
                        if (p.courseId === courseId) {
                            const newProgress = { ...p.sectionProgress };
                            delete newProgress[sectionId];
                            return {
                                ...p,
                                sectionProgress: newProgress
                            };
                        }
                        return p;
                    })
                }), false, 'completeSection'),

                getCourseProgress: (courseId) =>
                    get().progresses.find(p => p.courseId === courseId),

                resetProgress: (courseId, sectionId) => set((state) => ({
                    progresses: state.progresses.map(p => {
                        if (p.courseId === courseId) {
                            if (sectionId) {
                                const newProgress = { ...p.sectionProgress };
                                delete newProgress[sectionId];
                                return {
                                    ...p,
                                    sectionProgress: newProgress
                                };
                            }
                            return {
                                courseId,
                                sectionProgress: {}
                            };
                        }
                        return p;
                    })
                }), false, 'resetProgress')
            }),
            {
                name: 'course-progress-storage',
                storage: createJSONStorage(() => localStorage),
            }
        )
        , {
            name: 'course-progress-storage',
        }
    )
);
