import { useCallback, useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getAgentsByTags, searchAgents } from '../api';
import { useAgentMarketplaceStore } from '../store';
import { AgentQuery, AgentsByTag, Agent } from '../schemas';

/**
 * 智能体商店Hook
 * 用于获取和管理智能体商店数据
 */
export const useAgentMarketplace = () => {
  // 从store中获取状态
  const { activeCategory, setActiveCategory, searchKeyword, setSearchKeyword } = useAgentMarketplaceStore();

  // 为每个分类维护独立的加载状态
  const [loadingCategories, setLoadingCategories] = useState<Record<string, boolean>>({});

  // 获取按标签分组的智能体列表
  const {
    data: agents,
    isLoading: isAgentsLoading,
    error: agentsError,
    refetch: refetchAgents,
  } = useQuery<AgentsByTag>({
    queryKey: ['agentsByTags'],
    queryFn: async () => (await getAgentsByTags()).data,
  });

  // 从智能体数据中提取标签，而不是单独请求
  const tags = useMemo(() => {
    if (!agents || !agents.data) return [];
    // 直接从agentsData中获取所有标签（即对象的键）
    return Object.keys(agents.data);
  }, [agents]);

  // 处理分类切换
  const handleCategoryChange = useCallback(
    (category: string) => {
      setActiveCategory(category);
    },
    [setActiveCategory],
  );

  const {
    data: searchResult,
    isLoading: isSearchLoading,
    error: searchError,
    refetch: research,
  } = useQuery<Agent[]>({
    queryKey: ['agentsSearchResult'],
    enabled: false,
    queryFn: async () => {
      if (searchKeyword === '') return [];
      const defaultQuery: AgentQuery = { page: 1, page_size: 10, keyword: searchKeyword };
      return (await searchAgents(defaultQuery)).data.data;
    },
  });

  /**
   * 加载更多智能体
   * @param tag 当前标签
   * @param existingIds 已加载的智能体ID列表
   * @returns 新加载的智能体列表
   */
  const loadMore = useCallback(async (tag: string, existingIds: string[]) => {
    try {
      // 设置当前分类的加载状态为true
      setLoadingCategories((prev) => ({ ...prev, [tag]: true }));

      // 构建查询参数
      const query: AgentQuery = {
        page: 1,
        page_size: 4,
        tags: [tag],
        excludeIds: existingIds,
      };

      // 调用搜索API
      const result = await searchAgents(query);

      // 返回新加载的智能体列表
      return result.data.data;
    } catch (error) {
      console.error(`加载更多智能体失败: ${error}`);
      throw error;
    } finally {
      // 设置当前分类的加载状态为false
      setLoadingCategories((prev) => ({ ...prev, [tag]: false }));
    }
  }, []);

  return {
    // 数据
    tags,
    agents,
    searchResult,

    // 状态
    activeCategory,
    searchKeyword,
    loadingCategories,

    // 加载状态
    isAgentsLoading,
    isLoading: isAgentsLoading,
    isSearchLoading,

    // 错误状态
    agentsError,
    searchError,

    // 操作方法
    handleCategoryChange,
    refetchAgents,
    research,
    setSearchKeyword,
    loadMore,
  };
};

export default useAgentMarketplace;
