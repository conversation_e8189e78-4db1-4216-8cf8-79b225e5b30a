import {z} from "zod";

export const DataSetInCreateSchema = z.object({
  id: z.string().describe("数据集ID"),
  name: z.string().describe("数据集名称"),
  description: z.string().optional().describe("数据集描述"),
  permission: z.string().optional().default("only_me").describe("数据集权限"),
  data_source_type: z.string().optional().default("upload_file").describe("数据源类型"),
  indexing_technique: z.string().optional().default("high_quality").describe("索引技术"),
  created_by: z.string().optional().describe("创建者ID"),
  createdAt: z.number().optional().describe("创建时间戳")
});


export const DataSetCreateResponseSchema = z.object({
  dataset: DataSetInCreateSchema.describe("数据集"),
  documents: z.array(z.any()).describe("文档列表")
});

export type DataSetCreateResponse = z.infer<typeof DataSetCreateResponseSchema>;
