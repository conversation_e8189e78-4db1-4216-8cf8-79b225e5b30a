import {API_BASE_URL, ResponsePayloads} from "@/common";
import {useUserStore} from "@/user/stores";
import {request} from "@/common/utils";
import {DataSetCreateResponse} from "./types";

/**
 * 创建知识库
 * @param files 要上传的文件列表
 * @param name 知识库名称（可选）
 * @param description 知识库描述（可选）
 * @returns 创建结果，包含数据集信息和文档列表
 */
export async function createKnowledge(
  files: File[]
): Promise<ResponsePayloads<DataSetCreateResponse>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const formData = new FormData();

  // 添加文件到表单
  files.forEach(file => {
    formData.append('files', file);
  });

  const response = await request(`${API_BASE_URL}/knowledge/create`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  if (!response.ok) {
    throw new Error(`创建知识库失败: ${response.statusText}`);
  }

  return response.json();
}
