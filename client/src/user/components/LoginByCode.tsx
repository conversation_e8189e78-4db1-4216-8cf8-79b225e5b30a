import {forwardRef, useImperativeHandle, useState} from 'react';
import {Input, Text} from "@mantine/core";
import {useForm, zodResolver} from "@mantine/form";
import {SmsLoginRequestSchema} from "../types";
import {useMutation} from "@tanstack/react-query";
import {notifications} from "@mantine/notifications";
import {sendVerificationCode} from "../api";

export interface LoginFormHandle {
    validate: () => Promise<{ phone: string; code: string } | null>;
}

const LoginByCode = forwardRef<LoginFormHandle>((_, ref) => {
    const [countdown, setCountdown] = useState(0);
    const form = useForm({
        initialValues: { phone: '', code: '' },
        validate: zodResolver(SmsLoginRequestSchema),
    });

    const sendCodeMutation = useMutation({
        mutationFn: sendVerificationCode,
        onSuccess: (response) => {
            if (response.error) {
                notifications.show({
                    title: '发送失败',
                    message: response.error.message,
                    color: 'red',
                });
            } else {
                notifications.show({
                    title: '发送成功',
                    message: '验证码已发送，请注意查收',
                    color: 'green',
                });
                setCountdown(60);
                const timer = setInterval(() => {
                    setCountdown((prev) => (prev > 0 ? prev - 1 : 0));
                }, 1000);
                setTimeout(() => clearInterval(timer), 60000);
            }
        },
        onError: (error) => {
            notifications.show({
                title: '发送失败',
                message: error.message,
                color: 'red',
            });
        }
    });

    useImperativeHandle(ref, () => ({
        validate: async () => {
            const result = form.validate();
            return result.hasErrors ? null : form.values;
        }
    }));

    const handleSendCode = () => {
        const phoneError = form.validateField('phone').hasError;
        if (phoneError || !form.values.phone) return;
        sendCodeMutation.mutate(form.values.phone);
    };

    return (
        <>
            <Input.Wrapper error={form.errors.phone}>
                <Input
                    radius={8}
                    error={form.errors.phone}
                    placeholder="请输入手机号"
                    value={form.values.phone}
                    onChange={(event) => form.setFieldValue('phone', event.currentTarget.value)}
                    rightSection={form.values.phone !== '' &&
                        <Input.ClearButton onClick={() => form.setFieldValue('phone', '')} />}
                    rightSectionPointerEvents="auto"
                    styles={{ wrapper: { '--input-height': '40px' } }}
                />
            </Input.Wrapper>

            <Input.Wrapper error={form.errors.code}>
                <Input
                    radius={8}
                    error={form.errors.code}
                    placeholder="短信验证码"
                    value={form.values.code}
                    onChange={(event) => form.setFieldValue('code', event.currentTarget.value)}
                    rightSection={
                        <Text
                            className={`cursor-pointer ${countdown > 0 ? 'text-gray-400' : 'hover:text-blue-500'}`}
                            fz={14}
                            onClick={handleSendCode}
                        >
                            {countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}
                        </Text>
                    }
                    rightSectionWidth={100}
                    rightSectionPointerEvents="auto"
                    styles={{ wrapper: { '--input-height': '40px' } }}
                />
            </Input.Wrapper>
        </>
    );
});

export default LoginByCode;


