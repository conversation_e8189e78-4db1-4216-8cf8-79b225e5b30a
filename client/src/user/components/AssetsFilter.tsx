/** @jsxImportSource @emotion/react */
import {useTheme} from '@/common/contexts';
import {css} from '@emotion/react';
import {Chip, Group, useMantineTheme} from '@mantine/core';
import {UserAssetFilter} from '../types';

interface AssetsFilterProps {
  filters: UserAssetFilter[];
  value: UserAssetFilter;
  onChange: (filter: UserAssetFilter) => void;
}

const AssetsFilter = ({ filters, value, onChange }: AssetsFilterProps) => {

  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();

  // 根据主题设置不同的颜色变量
  const isDark = actualColorScheme === 'dark';

  // 默认底色和文字颜色
  const defaultBgColor = isDark ? theme.colors.dark[6] : theme.colors.gray[1];
  const defaultTextColor = isDark ? theme.colors.gray[3] : theme.colors.dark[5];

  // 选中时的底色和文字颜色
  const selectedBgColor = 'linear-gradient(90deg, rgba(86, 104, 255, 1) 0%, rgba(36, 162, 254, 1) 100%), rgba(50, 107, 255, 1)';
  const selectedTextColor = '#ffffff';

  return (
    <Chip.Group multiple={false} value={value?.value} onChange={(value: string) => {
      const filter = filters.find(f => f.value === value);
      if (filter) {
        onChange(filter);
      }
    }}>
      <Group gap="md">
        {filters.map(tag => (
          <Chip
            key={tag.value}
            value={tag.value}
            css={css`
              & label {
                background-color: ${defaultBgColor} !important;
                border: 0 !important;
                color: ${defaultTextColor} !important;
              }

              & label[data-checked] {
                background: ${selectedBgColor} !important;
                color: ${selectedTextColor} !important;
              }
            `}
            styles={{
              root: {
                '--chip-fz': '14px',
                '--chip-size': '40px',
                '--chip-padding': '13px',
                '--chip-checked-padding': '13px',
                '--chip-radius': '10px',
                '--chip-bg': 'transparent',
                '--chip-color': '#2763FC',
                '--chip-bd': '1px solid #2763FC',
                transition: 'background-color 0.2s ease'
              },
              label: {
                backgroundColor: 'var(--chip-bg)',
                fontWeight: '500',
                color: actualColorScheme === 'dark' ? theme.colors.gray[4] : theme.colors.dark[5],
              },
              input: {
                display: 'none'
              },
              iconWrapper: {
                display: 'none'
              }
            }}
          >
            {tag.label}
          </Chip>
        ))}
      </Group>
    </Chip.Group>
  );
};

export default AssetsFilter;
AssetsFilter.displayName = 'AssetsFilter';
