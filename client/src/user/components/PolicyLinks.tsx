import {Text} from '@mantine/core';
import {Link} from 'react-router-dom';

interface PolicyLinksProps {
    className?: string;
}

export default function PolicyLinks({ className = '' }: PolicyLinksProps) {
    return (
        <Text className={className} lh="14px" fz="14" c={'#A6A6A6'} ta="center">
            我已阅读并同意
            <Link
                to="/terms-of-service"
                target="_blank"
                className="text-blue-700 hover:text-blue-500 mx-1"
            >
                《用户协议》
            </Link>
            和
            <Link
                to="/privacy-policy"
                target="_blank"
                className="text-blue-700 hover:text-blue-500 mx-1"
            >
                《隐私政策》
            </Link>
        </Text>
    );
}
