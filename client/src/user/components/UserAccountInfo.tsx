import { useTheme } from '@/common/contexts';
import {
  Card,
  Group,
  Progress,
  Stack,
  Text,
  Title,
  useMantineTheme
} from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { fetchUserAccount } from '../api';
import { RiFlashlightLine } from 'react-icons/ri';

/**
 * 用户账户信息组件
 * 显示用户账户余额、剩余体验次数和剩余token数量
 */
const UserAccountInfo = () => {
  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();

  // 获取用户账户信息
  const { data, isLoading, error } = useQuery({
    queryKey: ['userAccount'],
    queryFn: fetchUserAccount,
    refetchOnWindowFocus: false
  });

  const accountInfo = data?.data;

  // 计算体验次数进度条百分比（假设最大值为100）
  const experienceProgress = accountInfo
    ? Math.min((accountInfo.remaining_experience_count / 100) * 100, 100)
    : 0;

  // 计算token进度条百分比（假设最大值为10000）
  const tokenProgress = accountInfo
    ? Math.min((accountInfo.remaining_tokens / 10000) * 100, 100)
    : 0;

  return (
    <Card
      shadow="sm"
      padding="lg"
      radius="md"
      withBorder
      style={{
        backgroundColor:
          actualColorScheme === 'dark' ? theme.colors.dark[7] : theme.white,
        borderColor:
          actualColorScheme === 'dark'
            ? theme.colors.dark[5]
            : theme.colors.gray[3]
      }}
    >
      <Title
        order={3}
        mb={20}
        c={
          actualColorScheme === 'dark'
            ? theme.colors.gray[0]
            : theme.colors.dark[7]
        }
      >
        账户使用情况
      </Title>

      {isLoading ? (
        <Text>加载中...</Text>
      ) : error ? (
        <Text c="red">加载失败</Text>
      ) : accountInfo ? (
        <Stack gap="md">
          {/* 账户余额 */}
          {/* <Card 
            padding="md" 
            radius="md"
            style={{ 
              backgroundColor: actualColorScheme === 'dark' ? theme.colors.dark[6] : theme.colors.gray[0],
            }}
          >
            <Group gap="xs" mb={5}>
              <RiCoinLine size={20} color={theme.colors.yellow[5]} />
              <Text fw={500} c={actualColorScheme === 'dark' ? theme.colors.gray[3] : theme.colors.dark[6]}>
                账户余额
              </Text>
            </Group>
            <Text size="xl" fw={700} c={actualColorScheme === 'dark' ? theme.white : theme.colors.dark[7]}>
              {formatCurrency(accountInfo.balance, accountInfo.currency)}
            </Text>
          </Card> */}
          {/* 剩余token */}
          <Card
            padding="md"
            radius="md"
            style={{
              backgroundColor:
                actualColorScheme === 'dark'
                  ? theme.colors.dark[6]
                  : theme.colors.gray[0]
            }}
          >
            <Group gap="xs" mb={5}>
              <RiFlashlightLine size={20} color={theme.colors.green[5]} />
              <Text
                fw={500}
                c={
                  actualColorScheme === 'dark'
                    ? theme.colors.gray[3]
                    : theme.colors.dark[6]
                }
              >
                剩余Token
              </Text>
            </Group>
            <Text
              size="xl"
              fw={700}
              c={
                actualColorScheme === 'dark'
                  ? theme.white
                  : theme.colors.dark[7]
              }
            >
              {accountInfo.remaining_tokens.toLocaleString()} tokens
            </Text>
            <Progress
              value={tokenProgress}
              mt={10}
              size="sm"
              color={theme.colors.green[5]}
              radius="xl"
            />
          </Card>
          {/* 剩余体验次数 */}
          <Card
            padding="md"
            radius="md"
            style={{
              backgroundColor:
                actualColorScheme === 'dark'
                  ? theme.colors.dark[6]
                  : theme.colors.gray[0]
            }}
          >
            <Group gap="xs" mb={5}>
              <RiFlashlightLine size={20} color={theme.colors.blue[5]} />
              <Text
                fw={500}
                c={
                  actualColorScheme === 'dark'
                    ? theme.colors.gray[3]
                    : theme.colors.dark[6]
                }
              >
                剩余体验次数
              </Text>
            </Group>
            <Text
              size="xl"
              fw={700}
              c={
                actualColorScheme === 'dark'
                  ? theme.white
                  : theme.colors.dark[7]
              }
            >
              {accountInfo.remaining_experience_count} 次
            </Text>
            <Progress
              value={experienceProgress}
              mt={10}
              size="sm"
              color={theme.colors.blue[5]}
              radius="xl"
            />
          </Card>
        </Stack>
      ) : (
        <Text>暂无账户信息</Text>
      )}
    </Card>
  );
};

export default UserAccountInfo;
