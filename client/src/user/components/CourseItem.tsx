import {useTheme} from '@/common/contexts';
import {<PERSON><PERSON>, Card, Group, Stack, Text, Title, useMantineTheme} from '@mantine/core';
import {Link} from 'react-router-dom';
import {UserAssetItem} from '../types';
import dayjs from 'dayjs';
import {useCourseProgressStore} from '@/course/stores';

const CourseCard = ({ course }: { course: UserAssetItem }) => {
    const { actualColorScheme } = useTheme();
    const theme = useMantineTheme();

    const progress = useCourseProgressStore((state) =>
        state.getCourseProgress(Number(course.asset_id))
    );

    const totalSections = course.details?.sections?.length || 0;
    const completedSections = progress ? Object.keys(progress.sectionProgress).length : 0;
    const progressPercent = totalSections > 0
        ? Math.round((completedSections / totalSections) * 100)
        : 0;

    return (
        <Link to={`/course/${course.asset_id}`} style={{ textDecoration: 'none', color: 'inherit' }}>
            <Card styles={{
                root: {
                    display: 'flex',
                    flexDirection: 'row',
                    width: '100%',
                    padding: 0,
                    marginBottom: '24px',
                    backgroundColor: 'transparent',
                },
                section: {
                    flex: '0 0 auto',
                    width: '246px',
                    height: '138px',
                    margin: 0,
                    borderRadius: '4px',
                    backgroundColor: '#D8D8D8',
                    overflow: 'hidden',
                },
            }} gap="35px">
                <Card.Section>
                    <img className="object-cover" w="100%" h="100%" src={course.details?.cover_image} alt={course.details?.cover_image} />
                </Card.Section>

                <Stack
                    className="relative w-2/3 border-b border-solid"
                    h={138}
                    gap={10}
                    style={{
                        borderColor: actualColorScheme === 'dark' ? theme.colors.gray[8] : theme.colors.gray[2],
                    }}
                >
                    <Title
                        fz={18}
                        fw={500}
                        c={actualColorScheme === 'dark' ? theme.colors.gray[4] : theme.colors.dark[5]}
                        order={3}
                        lineClamp={2}
                    >
                        {course.details?.title}
                    </Title>
                    <Group gap={16}>
                        <Text className='mt-3' fz={12} fw={500} c={'#F53E19'} inline>已学{progressPercent}%</Text>
                        <Text className='mt-3' fz={12} fw={500} c={'#666666'} inline>购买时间：{dayjs(course.created_at).format('YYYY-MM-DD')}</Text>

                        {/* <Text className='mt-3' fz={12} fw={500} c={'#999999'} inline>课程已下架，无法正常观看，可点击删除课程</Text> */}
                    </Group>

                    <Group className="absolute bottom-25px w-full" justify="space-between" align="center">
                        {/* <Badge styles={{
                            root: {
                                '--badge-bg': '#E6F2FF',
                                '--badge-color': '#828D99',
                                '--badge-fz': '10px',
                                '--badge-height': '18px',
                                '--badge-padding-x': '4px',
                                '--badge-radius': '1px',
                            }
                        }}>
                            我的已购
                        </Badge> */}

                        <Button
                            variant="gradient"
                            gradient={{ from: 'rgba(86, 104, 255, 1)', to: 'rgba(36, 162, 254, 1)', deg: 90 }}
                            fw={500}
                            styles={{
                                root: {
                                    '--button-bg': '#2763FC',
                                    '--button-color': '#FFFFFF',
                                    '--button-fz': '12px',
                                    '--button-height': '32px',
                                    '--button-padding-x': '21px',
                                    '--button-radius': '10px',
                                }
                            }}
                        >
                            继续学习
                        </Button>

                        {/* <Button c={'#666666'} fw={500} styles={{
                            root: {
                                '--button-bg': '#E6E6E6',
                                '--button-color': '#666666',
                                '--button-fz': '12px',
                                '--button-height': '32px',
                                '--button-padding-x': '21px',
                                '--button-radius': '10px',
                            }
                        }}>
                            删除
                        </Button> */}
                    </Group>
                </Stack>
            </Card>
        </Link>
    );
};

export default CourseCard;
CourseCard.displayName = "CourseCard";
