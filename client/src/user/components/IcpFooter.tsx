import { Box, Text, useMantineTheme } from '@mantine/core';
import { useTheme } from '@/common/contexts';
import { useSiteSettingsStore } from '@/common/stores';
import { RiLinkM } from 'react-icons/ri';

/**
 * ICP备案号页脚组件，用于登录页面底部
 */
const IcpFooter = () => {
  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();
  const { settings } = useSiteSettingsStore();

  if (!settings?.icp_filing) {
    return null;
  }

  return (
    <Box
      className="flex justify-center items-center py-2"
      style={{
        color: actualColorScheme === 'dark' ? theme.colors.gray[5] : theme.colors.gray[6],
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: actualColorScheme === 'dark' ? '#1A1B1E' : '#1A1B1E',
        zIndex: 1000,
      }}
    >
      <Text size="xs" className="flex items-center gap-1">
        <RiLinkM size={14} /> {/* 使用链接图标 */}
        <a
          href="https://beian.miit.gov.cn/"
          target="_blank"
          rel="noopener noreferrer"
          style={{
            color: 'inherit',
            textDecoration: 'none',
          }}
        >
          {settings.icp_filing}
        </a>
      </Text>
    </Box>
  );
};

export default IcpFooter;
