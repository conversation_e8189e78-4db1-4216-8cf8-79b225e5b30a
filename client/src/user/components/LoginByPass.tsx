import {forwardRef, useImperativeHandle} from 'react';
import {Group, Input, PasswordInput, Text} from "@mantine/core";
import {useForm, zodResolver} from "@mantine/form";
import {LoginRequest, LoginRequestSchema} from "../types";
import {Link, useNavigate} from "react-router-dom";
import {useSiteSettingsStore} from "@/common/stores";
import {useMutation} from "@tanstack/react-query";
import {notifications} from "@mantine/notifications";
import {login} from "../api";
import {useUserStore} from "../stores";

interface LoginFormHandle {
    validate: () => Promise<LoginRequest | null>;
}

const LoginByPass = forwardRef<LoginFormHandle>((_, ref) => {
    const form = useForm({
        initialValues: {
            username: '',
            password: ''
        },
        validate: zodResolver(LoginRequestSchema),
    });
    const { settings } = useSiteSettingsStore();
    const { setToken, setUserInfo } = useUserStore();
    const navigate = useNavigate();
    const loginMutation = useMutation({
        mutationFn: login,
        onSuccess: (response) => {
            if (response.data) {
                setToken(response.data.token)
                setUserInfo(response.data.user_info)
                navigate('/')
            } else if (response.error) {
                notifications.show({
                    title: response.error.type,
                    message: response.error.message,
                    color: 'red',
                })
            }
        },
        onError: (error) => {
            notifications.show({
                title: '网络错误',
                message: error.message,
                color: 'red',
            })
        }
    })

    useImperativeHandle(ref, () => ({
        validate: async () => {
            const result = form.validate();
            return result.hasErrors ? null : form.values;
        }
    }));

    return <>
        <Input.Wrapper error={form.errors.username}>
            <Input
                error={form.errors.username}
                radius={8}
                placeholder="请输入用户名"
                value={form.values.username}
                onChange={(event) => form.setFieldValue('username', event.currentTarget.value)}
                rightSection={form.values.username !== '' ? <Input.ClearButton onClick={() => form.setFieldValue('username', '')} /> : undefined}
                rightSectionPointerEvents="auto"
                styles={{
                    wrapper: {
                        '--input-height': '40px'
                    },
                }}
            />
        </Input.Wrapper>
        <PasswordInput
            radius={8}
            value={form.values.password}
            onChange={(event) => form.setFieldValue('password', event.currentTarget.value)}
            placeholder="请输入密码"
            error={form.errors.password}
            styles={{
                input: {
                    height: '40px'
                },
            }} />

        <Group justify="space-between" mt="xs">
            <Text component={Link} to="/forget-password" c="#326BFF" size="sm">
                忘记密码
            </Text>
            {settings?.allow_registration && (
                <Text component={Link} to="/register" c="#326BFF" size="sm">
                    立即注册
                </Text>
            )}
        </Group>

    </>;
});

export default LoginByPass;

