/** @jsxImportSource @emotion/react */
import {css} from '@emotion/react';
import {useTheme} from '@/common/contexts';
import {Avatar, Button, Card, Group, Stack, Text, Title, useMantineTheme} from '@mantine/core';
import {UserAssetItem} from '../types';
import {useUserStore} from '@/user/stores';
import {deleteAgent} from '@/agents/api';
import {notifications} from '@mantine/notifications';
import {FiEdit, FiTrash2} from 'react-icons/fi';
import {useMutation} from '@tanstack/react-query';
import {useNavigate} from 'react-router-dom';

const AppItem = ({ app ,onDelete}: { app: UserAssetItem ,onDelete:()=>void }) => {
  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();
  const user = useUserStore(state => state.userInfo);
  const navigate = useNavigate();
  const { asset_id, asset_name, details } = app;

  const deleteAgentMutation = useMutation({
    mutationFn: () => deleteAgent(asset_id),
    onSuccess: () => {
      notifications.show({
        title: '删除成功',
        message: `已成功删除智能体 ${asset_name}`,
        color: 'green',
      });
      onDelete();
    },
    onError: (error: Error) => {
      notifications.show({
        title: '删除失败',
        message: error.message,
        color: 'red',
      });
    }
  });

  const handleDelete = () => {
    if (window.confirm(`确定要删除智能体 ${asset_name} 吗？`)) {
      deleteAgentMutation.mutate();
    }
  };

  const handleEdit = () => {
    navigate(`/marketplace/edit?agent_id=${asset_id}`);
  };

  const isOwner = details?.owner_id === user?.id;

  return (
    <Card
      padding="24px 16px"
      radius={8}
      withBorder
      css={css`
        &:hover{
          box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.08);
        }
      `}
      styles={{
        root: {
          flexDirection: 'row',
          borderColor: actualColorScheme === 'dark' ? theme.colors.gray[8] : theme.colors.gray[1],
        }
      }}
    >
      <Group className='w-full' gap="12px" align="flex-start">
        <Avatar
          size="32px"
          radius="xl"
          src={null}
          color="blue"
          style={{
            backgroundColor: 'app.icon_background',
            fontSize: '24px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          {asset_name}
        </Avatar>

        <Stack
          gap="10px"
          styles={{
            root: {
              width: 'calc(100% - 44px)',
            }
          }}
        >
          <Group justify="space-between" w="100%">
            <Title
                fz={18}
                fw={500}
                c={actualColorScheme === 'dark' ? theme.colors.gray[4] : theme.colors.dark[5]}
                order={3}
                lineClamp={2}
                style={{ flex: 1 }}
            >
                {details?.name}
            </Title>

            {isOwner && (
              <Group gap={8}>
                <Button
                  variant="subtle"
                  color="blue"
                  size="xs"
                  leftSection={<FiEdit size={14} />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEdit();
                  }}
                  styles={{
                    root: {
                      padding: '4px 8px',
                    }
                  }}
                >
                  编辑
                </Button>
                <Button
                  variant="subtle"
                  color="red"
                  size="xs"
                  leftSection={<FiTrash2 size={14} />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete();
                  }}
                  styles={{
                    root: {
                      padding: '4px 8px',
                    }
                  }}
                >
                  删除
                </Button>
              </Group>
            )}
          </Group>

          <Text h={54} fz={12} fw={500} c={'#666F8D'} lh="18px" lineClamp={3}>
            {details?.description}
          </Text>
        </Stack>
      </Group>
    </Card>
  );
}

export default AppItem;
