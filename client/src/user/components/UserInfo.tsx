import {useTheme} from '@/common/contexts';
import {Avatar, Box, Divider, Text, useMantineTheme} from "@mantine/core";
import {ossUrl} from "@/common";

const UserInfo = () => {
    const { actualColorScheme } = useTheme();
    const theme = useMantineTheme();

  return (
    <Box className="flex flex-col w-full px-8" style={{background: `url(${ossUrl('/images/userinfo_bg.svg')}) center -35px/100% auto no-repeat`}}>
      <Avatar className="mt-15 border border-solid border-white" variant="white" size={76} src="https://img.js.design/assets/img/62450720e60b851db6b5676b.png" alt="it's me" />
      <Text
        mt={10}
        fz={20}
        fw={500}
        lh="30px"
        c={actualColorScheme === 'dark' ? theme.colors.gray[2] : theme.colors.dark[5]}
      >
        AI 智元
      </Text>
      <Text
        h={20}
        mt={5}
        fz={14}
        fw={500}
        c={"#A6A6A6"}
      >
        这里是个性签名~
      </Text>
      <Divider mt={10} color={actualColorScheme === 'dark' ? theme.colors.gray[6] : theme.colors.gray[2]} />
    </Box>
  );
};

export default UserInfo;
