import {useState} from 'react';
import {Button, Checkbox, Container, Input, PasswordInput, Stack, Text, Title} from '@mantine/core';
import {PolicyLinks} from '@/user/components';
import {useMutation} from '@tanstack/react-query';
import {register, sendVerificationCode} from '../api';
import {notifications} from '@mantine/notifications';
import {useNavigate} from 'react-router-dom';
import {useForm, zodResolver} from '@mantine/form';
import {RegisterRequestSchema} from '../types';
import {useUserStore} from '../stores';

const Register = () => {
    const navigate = useNavigate();
    const [agreed, setAgreed] = useState(false);
    const [countdown, setCountdown] = useState(0);

    const form = useForm({
        initialValues: {
            username: '',
            password: '',
            phone: '',
            code: ''
        },
        validate: zodResolver(RegisterRequestSchema),
    });

    const sendCodeMutation = useMutation({
        mutationFn: sendVerificationCode,
        onSuccess: (response) => {
            if (response.error) {
                notifications.show({
                    title: '发送失败',
                    message: response.error.message,
                    color: 'red',
                });
            } else {
                notifications.show({
                    title: '发送成功',
                    message: '验证码已发送，请注意查收',
                    color: 'green',
                });
                setCountdown(60);
                const timer = setInterval(() => {
                    setCountdown((prev) => (prev > 0 ? prev - 1 : 0));
                }, 1000);
                setTimeout(() => clearInterval(timer), 60000);
            }
        },
        onError: (error) => {
            notifications.show({
                title: '发送失败',
                message: error.message,
                color: 'red',
            });
        }
    });
    const { setToken, setUserInfo } = useUserStore()
    const registerMutation = useMutation({
        mutationFn: register,
        onSuccess: (response) => {
            if (response.data) {
                notifications.show({
                    title: '注册成功',
                    message: '即将跳转到登录页面',
                    color: 'green',
                });
                setToken(response.data.token)
                setUserInfo(response.data.user_info)
                navigate('/')
            } else if (response.error) {
                notifications.show({
                    title: response.error.type,
                    message: response.error.message,
                    color: 'red',
                });
            }
        },
        onError: (error) => {
            notifications.show({
                title: '注册失败',
                message: error.message,
                color: 'red',
            });
        }
    });

    const handleSendCode = () => {
        const { phone } = form.values;
        const phoneError = form.validateField('phone').hasError;

        if (phoneError || !phone) {
            return;
        }
        sendCodeMutation.mutate(phone);
    };

    const handleRegister = async () => {
        const { hasErrors } = form.validate();

        if (hasErrors) return;

        if (!agreed) {
            notifications.show({ title: '提示', message: '请先阅读并同意协议', color: 'red' });
            return;
        }
        await registerMutation.mutateAsync(form.values);
        form.reset();
        setAgreed(false);
    };

    return (
        <Container size={600} my={80} style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <Title ta="center" className="title" style={{ fontSize: '2.5rem' }}>
                注册
            </Title>
            <form>
                <Stack
                    className='relative flex-auto'
                    h="100%"
                    p="40px 64px 0"
                    align="stretch"
                    justify="flex-start"
                >
                    <Stack
                        align="stretch"
                        justify="flex-start"
                        gap="12px"
                    >
                        <Input.Wrapper error={form.errors.username}>
                            <Input
                                {...form.getInputProps('username')}
                                radius={8}
                                placeholder="请设置用户名"
                                rightSection={
                                    form.values.username && <Input.ClearButton onClick={() => form.setFieldValue('username', '')} />
                                }
                                rightSectionPointerEvents="auto"
                                styles={{
                                    wrapper: {
                                        '--input-height': '40px'
                                    },
                                }}
                            />
                        </Input.Wrapper>
                        <PasswordInput
                            {...form.getInputProps('password')}
                            radius={8}
                            placeholder="请设置登录密码"
                            error={form.errors.password}
                            styles={{
                                input: {
                                    height: '40px'
                                },
                            }}
                        />
                        <Input.Wrapper error={form.errors.phone}>
                            <Input
                                {...form.getInputProps('phone')}
                                radius={8}
                                placeholder="请输入手机号"
                                rightSection={
                                    form.values.phone && <Input.ClearButton onClick={() => form.setFieldValue('phone', '')} />
                                }
                                rightSectionPointerEvents="auto"
                                styles={{
                                    wrapper: {
                                        '--input-height': '40px'
                                    },
                                }}
                            />
                        </Input.Wrapper>
                        <Input.Wrapper error={form.errors.code}>
                            <Input
                                {...form.getInputProps('code')}
                                radius={8}
                                placeholder="短信验证码"
                                rightSection={
                                    <Text
                                        className={`cursor-pointer ${countdown > 0 ? 'text-gray-400' : 'hover:text-blue-500'}`}
                                        fz={14}
                                        onClick={handleSendCode}
                                    >
                                        {countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}
                                    </Text>
                                }
                                rightSectionWidth={100}
                                rightSectionPointerEvents="auto"
                                styles={{
                                    wrapper: {
                                        '--input-height': '40px',
                                        '--text-color': '#326BFF',
                                    },
                                }}
                            />
                        </Input.Wrapper>
                    </Stack>

                    <Stack
                        align="stretch"
                        justify="flex-start"
                        gap="16px"
                    >
                        <Button
                            type="button"
                            variant="gradient"
                            gradient={{ from: 'rgba(86, 104, 255, 1)', to: 'rgba(36, 162, 254, 1)', deg: 90 }}
                            radius={8}
                            fullWidth
                            loading={registerMutation.isPending}
                            onClick={handleRegister}
                        >
                            注册
                        </Button>

                        <Checkbox
                            label={<PolicyLinks />}
                            checked={agreed}
                            onChange={(e) => setAgreed(e.currentTarget.checked)}
                            styles={{
                                root: {
                                    '--checkbox-color': '#326BFF',
                                    '--checkbox-radius': '2px',
                                    '--checkbox-size': '14px',
                                },
                                body: {
                                    display: 'flex',
                                    alignItems: 'center',
                                },
                                label: {
                                    paddingLeft: '4px',
                                }
                            }}
                        />
                    </Stack>
                </Stack>
            </form>
        </Container>
    )
}

export default Register;
