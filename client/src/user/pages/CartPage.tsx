import {useCartStore} from '@/agents/stores/useCartStore';
import {
    Box,
    Button,
    Container,
    Group,
    Modal,
    Radio,
    Select,
    Stack,
    Table,
    Text,
    Title,
    useMantineTheme
} from '@mantine/core';
import {notifications} from '@mantine/notifications';
import {useMutation} from '@tanstack/react-query';
import {QRCodeSVG} from 'qrcode.react';
import {useState} from 'react';
import {FaAlipay, FaWeixin} from 'react-icons/fa';
import {createOrder} from '../api';
import {OrderItem, PaymentMethod, PricingType} from '../types';
import {Link} from 'react-router-dom';
import {useTheme} from '@/common/contexts';
import {Empty} from "@/common/components";

export default function CartPage() {
    const { actualColorScheme } = useTheme();
    const theme = useMantineTheme();

    const { items: cartItems, removeItem, clearCart } = useCartStore();
    const [pricingType, setPricingType] = useState<PricingType>('monthly');
    const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('alipay');
    const [wechatPaymentUrl, setWechatPaymentUrl] = useState<string | null>(null);
    const [orderId, setOrderId] = useState<string | null>(null);

    // 计算总价
    const totalPrice = cartItems.reduce((sum: number, item) => {
        return sum + Number(item.app?.[pricingType === 'monthly' ? 'monthly_price' : 'yearly_price'] || 0);
    }, 0);

    // 删除处理保持不变
    const handleRemove = (id: string) => {
        removeItem(id);
    };

    const paymentType = [
        { label: '支付宝', value: 'alipay' },
        { label: '微信支付', value: 'wechatpay' },
    ]
    const paymentRows = paymentType.map((row) => (
        <Table.Tr h={60} key={row.value}>
            <Table.Td>
                <Radio.Card withBorder={false} value={row.value}>
                    <Group gap={40}>
                        <Radio.Indicator />
                        <Group gap={8}>
                            {row.value === 'alipay' && <FaAlipay size={24} color="#02A9F1" />}
                            {row.value === 'wechatpay' && <FaWeixin size={24} color="#09BB07" />}
                            <Text
                                fz={14}
                                fw={500}
                                c={actualColorScheme === 'dark' ? theme.colors.gray[3] : theme.colors.dark[6]}
                            >
                                {row.label}
                            </Text>
                        </Group>
                    </Group>
                </Radio.Card>
            </Table.Td>
        </Table.Tr>
    ));

    // 支付处理保持不变
    const handlePayment = useMutation({
        mutationFn: createOrder,
        onSuccess: (data) => {
            if (data.data) {
                notifications.show({
                    title: '提示',
                    message: '订单已创建，正在跳转...',
                    color: 'green'
                });
                clearCart();
                setOrderId(data.data.order_id);
                if (data.data?.payment_url) {
                    if (paymentMethod === 'wechatpay') {
                        setWechatPaymentUrl(data.data.payment_url);
                    } else {
                        window.open(data.data.payment_url, '_blank');
                    }
                }
            } else {
                notifications.show({
                    title: '支付失败',
                    message: data.error?.message || '未知错误',
                    color: 'red'
                });
            }
        },
        onError: (error) => {
            notifications.show({
                title: '请求失败',
                message: error.message,
                color: 'red'
            });
        }
    });

    // 调整后的表格行，直接使用购物车数据
    const rows = cartItems.map((item) => (
        <Table.Tr h={'80px'} key={item.app?.id}>
            <Table.Td>
                <Text fz={14} fw={500} c={actualColorScheme === 'dark' ? theme.colors.gray[3] : theme.colors.dark[6]}>{item.app?.name}</Text>
            </Table.Td>
            <Table.Td>
                <Select
                    allowDeselect={false}
                    size="xs"
                    value={pricingType}
                    onChange={(value) => setPricingType(value as 'monthly' | 'yearly')}
                    data={[
                        { value: 'monthly', label: '月付' },
                        // { value: 'yearly', label: '年付' }
                    ]}
                    style={{ width: 100 }}
                />
            </Table.Td>
            <Table.Td>
                <Group gap="xs">
                    <Text fz={16} fw={500} c={'#FF7A3E'}>
                        ¥{item.app?.[pricingType === 'monthly' ? 'monthly_price' : 'yearly_price'] || '--'}
                    </Text>
                </Group>
            </Table.Td>
            <Table.Td>
                <Group>
                    <Button
                        variant="light"
                        color="red"
                        size="xs"
                        onClick={() => handleRemove(item.app?.id || '')}
                    >
                        删除
                    </Button>
                </Group>
            </Table.Td>
        </Table.Tr>
    ));

    return (
        <Container size="md" py="xl">
            <Title order={3}>购物车</Title>

            <Table variant="vertical" mt={30} withRowBorders={false}>
                <Table.Thead>
                    <Table.Tr>
                        <Table.Th>应用</Table.Th>
                        <Table.Th>套餐</Table.Th>
                        <Table.Th>
                            <Group gap="xs">
                                价格
                            </Group>
                        </Table.Th>
                        <Table.Th>操作</Table.Th>
                    </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rows}</Table.Tbody>
            </Table>

            {cartItems?.length === 0 && (
                <Empty text="暂无收藏的智能体"></Empty>
            )}

            {cartItems && cartItems.length > 0 && (
                <Box>
                    <Title order={3} mt={54}>支付方式</Title>

                    <Radio.Group
                        value={paymentMethod}
                        onChange={(value) => {
                            setPaymentMethod(value as PaymentMethod);
                        }}
                    >
                        <Table>
                            <Table.Tbody>
                                {paymentRows}
                            </Table.Tbody>
                        </Table>
                    </Radio.Group>

                    <Group
                        mt={30}
                        align="center"
                        justify="flex-end"
                        gap={14}
                        styles={{
                            root: {
                                height: '126px',
                                padding: '0 28px',
                                backgroundColor: actualColorScheme === 'dark' ? theme.colors.dark[6] : theme.colors.gray[0],
                                borderRadius: '4px',
                            }
                        }}
                    >
                        <Stack
                            align="flex-end"
                        >
                            <Group align='flex-end' gap={0}>
                                <Text fz={12} fw={500} c={actualColorScheme === 'dark' ? theme.colors.gray[3] : theme.colors.dark[6]} inline>合计：</Text>
                                <Text lh="14px" span fz={18} fw={700} c={'#FF7A3E'}>¥{(Number(totalPrice) || 0).toFixed(2)}</Text>
                            </Group>

                            <Button
                                w={180}
                                size="md"
                                variant="filled"
                                color='#FF7A3E'
                                onClick={() => {
                                    if (!paymentMethod) {
                                        notifications.show({
                                            title: '请选择支付方式',
                                            message: '请先选择支付宝或微信支付',
                                            color: 'yellow'
                                        });
                                        return;
                                    }
                                    handlePayment.mutate({
                                        payment_method: paymentMethod,
                                        items: cartItems.map(item => ({
                                            asset_type: 'app',
                                            asset_id: item.app?.id || '',
                                            quantity: 1,
                                            unit_price: pricingType
                                        } as OrderItem))
                                    });
                                }}
                                disabled={!paymentMethod}
                                loading={handlePayment.isPending}
                            >
                                立即支付
                            </Button>
                        </Stack>
                    </Group>
                </Box>
            )}

            <Modal
                opened={!!wechatPaymentUrl}
                onClose={() => setWechatPaymentUrl(null)}
                title="微信扫码支付"
                centered
            >
                <Group justify="center" p="md">
                    {wechatPaymentUrl && (
                        <>
                            <QRCodeSVG
                                value={wechatPaymentUrl}
                                size={256}
                                level="H"
                                bgColor="#ffffff"
                                fgColor="#07c160"
                                title="微信支付二维码"
                                marginSize={4}
                            />
                            <Text c="dimmed" size="sm" mt="md">
                                请使用微信扫描二维码完成支付
                            </Text>
                            <Button
                                component={Link}
                                to={`/orderStatus?out_trade_no=${orderId}`}
                                fullWidth
                                mt="md"
                                variant="light"
                            >
                                我已完成支付
                            </Button>
                        </>
                    )}
                </Group>
            </Modal>
        </Container>
    );
}
