import {useTheme} from '@/common/contexts';
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LoginByCode, LoginByPass, PolicyLinks} from '@/user/components';
import {Button, Checkbox, Container, SegmentedControl, Stack, Text, Title} from '@mantine/core';
import {notifications} from '@mantine/notifications';
import {useRef, useState} from 'react';
import {useMutation} from '@tanstack/react-query';
import {login, smsLogin} from '../api';
import {useUserStore} from '../stores';
import {useNavigate} from 'react-router-dom';

const Login = () => {
    const [loginType, setLoginType] = useState('sms');
    const [agreed, setAgreed] = useState(false);
    const { actualColorScheme } = useTheme();
    const navigate = useNavigate();
    const { setToken, setUserInfo } = useUserStore();

    const formRef = useRef<any>(null);

    const loginMutation = useMutation({
        mutationFn: (data: any) =>
            loginType === 'account' ? login(data) : smsLogin(data),
        onSuccess: (response) => {
            if (response.data) {
                setToken(response.data.token);
                setUserInfo(response.data.user_info);
                navigate('/');
            } else if (response.error) {
                notifications.show({
                    title: response.error.type,
                    message: response.error.message,
                    color: 'red',
                });
            }
        },
        onError: (error) => {
            notifications.show({
                title: '网络错误',
                message: error.message,
                color: 'red',
            });
        }
    });

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!agreed) {
            notifications.show({ title: '提示', message: '请先阅读并同意协议', color: 'red' });
            return;
        }

        const formData = await formRef.current?.validate();
        if (formData) {
            loginMutation.mutate(formData);
        }
    };

    return (
        <Container size={600} my={80} pb={40} style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <Title ta="center" className="title" style={{ fontSize: '2.5rem' }}>
                欢迎回来
            </Title>
            <Text c="dimmed" size="lg" ta="center" mt={10}>
                请输入您的凭据以登录
            </Text>
            <Stack
                className='relative flex-auto'
                h="100%"
                p="40px 64px 0"
                align="stretch"
                justify="flex-start"
            >
                <Stack
                    align="stretch"
                    justify="flex-start"
                    gap="20px"
                >
                    <SegmentedControl
                        value={loginType}
                        onChange={setLoginType}
                        data={[
                            { label: '短信登录', value: 'sms' },
                            { label: '账号登录', value: 'account' },
                        ]}
                        styles={{
                            root: {
                                '--sc-label-color': actualColorScheme === 'dark' ? '#FFFFFF' : '#326BFF',
                            },
                        }}
                    />

                    {loginType === 'sms' ? <LoginByCode ref={formRef} /> : <LoginByPass ref={formRef} />}

                    <Button
                        variant="gradient"
                        gradient={{ from: 'rgba(86, 104, 255, 1)', to: 'rgba(36, 162, 254, 1)', deg: 90 }}
                        radius={8}
                        onClick={handleSubmit}
                        fullWidth
                        styles={{
                            root: {
                                '--button-height': '40px'
                            },
                        }}
                        loading={loginMutation.isPending}
                    >
                        登录
                    </Button>

                    <Checkbox
                        label={<PolicyLinks />}
                        checked={agreed}
                        onChange={(e) => setAgreed(e.currentTarget.checked)}
                        styles={{
                            root: {
                                '--checkbox-color': '#326BFF',
                                '--checkbox-radius': '2px',
                                '--checkbox-size': '14px',
                            },
                            body: {
                                display: 'flex',
                                alignItems: 'center',
                            },
                            label: {
                                paddingLeft: '4px',
                            }
                        }}
                    />
                </Stack>
            </Stack>
            <IcpFooter />
        </Container>
    )
}

export default Login;
Login.displayName = 'Login';
