import {useSearchParams} from 'react-router-dom';
import {useQuery} from '@tanstack/react-query';
import {Alert, Button, Container, Group, LoadingOverlay, Paper, Text, Title} from '@mantine/core';
import {fetchOrderDetail} from '../api';
import {TbAlertCircle, TbCheck} from 'react-icons/tb';
import {OrderStatus} from '../types';

export default function OrderStatusPage() {
    const [searchParams] = useSearchParams();
    const orderId = searchParams.get('out_trade_no');
    const tradeNo = searchParams.get('trade_no');

    const { data, isLoading, isError } = useQuery({
        queryKey: ['order', orderId],
        queryFn: () => fetchOrderDetail(orderId!),
        enabled: !!orderId
    });

    const renderContent = () => {
        if (!orderId) {
            return (
                <Alert variant="filled" color="red" title="错误" icon={<TbAlertCircle />}>
                    无效的订单号
                </Alert>
            );
        }

        if (isLoading) return <LoadingOverlay visible />;

        if (isError) {
            return (
                <Alert variant="filled" color="red" title="错误" icon={<TbAlertCircle />}>
                    订单查询失败，请稍后重试
                </Alert>
            );
        }

        const order = data?.data;
        const isSuccess = order?.status === OrderStatus.PAID;

        return (
            <Paper p="xl" shadow="sm" radius="md">
                <Group justify="center" mb="md">
                    <div style={{ textAlign: 'center' }}>
                        {isSuccess ? (
                            <>
                                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                    <TbCheck size={64} color="green" />
                                    <Title order={2} mt={4} c="green">支付成功</Title>
                                </div>
                            </>
                        ) : (
                            <>
                                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                    <TbAlertCircle size={64} color="orange" />
                                    <Title order={2} mt={4} c="orange">支付处理中</Title>
                                </div>
                            </>
                        )}
                    </div>
                </Group>

                <Group gap="xs" mb="sm">
                    <Text fw={500}>商户订单号：</Text>
                    <Text>{orderId}</Text>
                </Group>

                {tradeNo && (
                    <Group gap="xs" mb="sm">
                        <Text fw={500}>支付宝交易号：</Text>
                        <Text>{tradeNo}</Text>
                    </Group>
                )}

                {order && (
                    <>
                        <Group gap="xs" mb="sm">
                            <Text fw={500}>支付金额：</Text>
                            <Text c="blue">¥{order.amount}</Text>
                        </Group>
                        <Group gap="xs" mb="xl">
                            <Text fw={500}>支付方式：</Text>
                            <Text>{order.payment_method === 'alipay' ? '支付宝' : '微信支付'}</Text>
                        </Group>
                    </>
                )}

                <Button
                    variant="light"
                    fullWidth
                    component="a"
                    href="/"
                    mt="md"
                >
                    返回首页
                </Button>
            </Paper>
        );
    };

    return (
        <Container size="sm" py="xl">
            <Title order={1} ta="center" mb="xl">
                订单支付结果
            </Title>
            {renderContent()}
        </Container>
    );
}
