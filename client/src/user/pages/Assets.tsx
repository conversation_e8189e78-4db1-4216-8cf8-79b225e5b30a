import { AgentType } from '@/agents/types';
import { Pagination as PaginationType } from '@/common';
import { EmptyState } from '@/common/components';
import { useTheme } from '@/common/contexts';
import { useUserStore } from '@/user';
import {
  Box,
  Card,
  Center,
  Grid,
  Group,
  Pagination,
  Skeleton,
  Stack,
  Tabs,
  Text,
  useMantineTheme
} from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import React, { useState } from 'react';
import { BiErrorCircle } from 'react-icons/bi';
import { fetchUserAssets } from '../api';
import {
  AppItem,
  AssetsFilter,
  CourseItem,
  UserAccountInfo,
  UserInfo
} from '../components';
import { UserAssetFilter, UserAssetItem } from '../types';

const Assets = () => {
  const [pagination, setPagination] = useState({ page: 1, pageSize: 9 });
  const [activeTab, setActiveTab] = useState<string | null>('assets');
  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();

  const filters: UserAssetFilter[] = [
    {
      label: '我的智能体',
      value: 'app_chat',
      assetType: 'app',
      appMode: AgentType.CHAT
    },
    {
      label: '我的工作流',
      value: 'app_workflow',
      assetType: 'app',
      appMode: AgentType.WORKFLOW
    },
    {
      label: '我的课程',
      value: 'course',
      assetType: 'course'
    }
  ];
  const [currentFilter, setCurrentFilter] = useState<UserAssetFilter>(
    filters[0]
  );
  const {
    data: assetsPage,
    isLoading,
    isError,
    refetch
  } = useQuery<PaginationType<UserAssetItem> | undefined>({
    queryKey: ['userAssets', currentFilter, pagination],
    queryFn: async () => {
      const token = useUserStore.getState().token;
      if (!token) throw new Error('未登录');
      const res = await fetchUserAssets(
        currentFilter!.assetType!,
        currentFilter!.appMode,
        '',
        pagination.page,
        pagination.pageSize,
        true
      );
      return res.data;
    },
    enabled: !!currentFilter
  });

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handleTagFilter = (filter: UserAssetFilter) => {
    setCurrentFilter(filter);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const renderer = (item: UserAssetItem) => (
    <Grid.Col
      span={
        currentFilter.assetType === 'course'
          ? { base: 12, sm: 12, md: 12 }
          : { base: 12, sm: 6, md: 4 }
      }
    >
      {item.asset_type === 'course' && <CourseItem course={item} />}

      {item.asset_type === 'app' && <AppItem app={item} onDelete={refetch} />}
    </Grid.Col>
  );

  // 骨架屏加载组件
  const renderSkeletonUI = () => {
    const skeletonCount = 6;
    const isCourse = currentFilter.assetType === 'course';

    return (
      <Grid mt={30} style={{ maxWidth: '80%' }}>
        {Array(skeletonCount)
          .fill(0)
          .map((_, index) => (
            <Grid.Col
              key={index}
              span={
                isCourse
                  ? { base: 12, sm: 12, md: 12 }
                  : { base: 12, sm: 6, md: 4 }
              }
            >
              <Card shadow="sm" padding="lg" radius="md" withBorder>
                <Stack gap="md">
                  <Group>
                    <Skeleton circle height={40} />
                    <Skeleton height={24} width="60%" radius="md" />
                  </Group>
                  <Skeleton height={16} width="90%" radius="md" />
                  <Skeleton height={16} width="80%" radius="md" />
                  {isCourse ? <Skeleton height={100} radius="md" /> : null}
                  <Group mt="md" justify="space-between">
                    <Skeleton height={28} width="40%" radius="md" />
                    <Group gap="sm">
                      <Skeleton height={36} width={100} radius="md" />
                    </Group>
                  </Group>
                </Stack>
              </Card>
            </Grid.Col>
          ))}
      </Grid>
    );
  };

  return (
    <Box style={{ height: '100%', overflowY: 'auto' }}>
      <UserInfo />

      <Box pt={15} pr={32} pl={32}>
        <Tabs value={activeTab} onChange={setActiveTab} color="#326BFF">
          <Tabs.List>
            <Tabs.Tab value="assets" color="#326BFF">
              <Text
                lh="30px"
                fz={16}
                fw={500}
                c={
                  actualColorScheme === 'dark'
                    ? theme.colors.gray[3]
                    : theme.colors.dark[6]
                }
              >
                我的资产
              </Text>
            </Tabs.Tab>
            <Tabs.Tab value="account" color="#326BFF">
              <Text
                lh="30px"
                fz={16}
                fw={500}
                c={
                  actualColorScheme === 'dark'
                    ? theme.colors.gray[3]
                    : theme.colors.dark[6]
                }
              >
                账户信息
              </Text>
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="assets" pt="md">
            <AssetsFilter
              filters={filters}
              value={currentFilter}
              onChange={handleTagFilter}
            />

            {isLoading ? (
              renderSkeletonUI()
            ) : isError ? (
              <EmptyState
                title="加载失败"
                description="无法加载您的资源，请稍后再试"
                buttonText="重新加载"
                icon={<BiErrorCircle size={32} />}
                onButtonClick={() => refetch()}
                showButton={true}
              />
            ) : (
              <>
                <Grid mt={30} style={{ maxWidth: '80%' }}>
                  {assetsPage?.data?.length === 0 ? (
                    <Box w="100%">
                      <EmptyState
                        title="暂无资源"
                        description={`您还没有${currentFilter.label}，开始创建吧`}
                        showButton={false}
                      />
                    </Box>
                  ) : (
                    assetsPage?.data?.map(item => (
                      <React.Fragment key={item.asset_id}>
                        {renderer(item)}
                      </React.Fragment>
                    ))
                  )}
                </Grid>

                {/* 分页组件 */}
                {assetsPage && (assetsPage.data?.length ?? 0) > 0 && (
                  <Center>
                    <Pagination
                      color="#326BFF"
                      value={pagination.page}
                      onChange={handlePageChange}
                      total={Math.ceil(
                        (assetsPage?.total || 0) / pagination.pageSize
                      )}
                      mt="md"
                      mb="md"
                    />
                  </Center>
                )}
              </>
            )}
          </Tabs.Panel>

          <Tabs.Panel value="account" pt="md">
            <Box mt={20} style={{ maxWidth: '600px' }}>
              <UserAccountInfo />
            </Box>
          </Tabs.Panel>
        </Tabs>
      </Box>
    </Box>
  );
};

export default Assets;
Assets.displayName = 'Assets';
