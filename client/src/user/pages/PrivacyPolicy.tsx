import {Container, useMantineColorScheme} from '@mantine/core';
import {MarkdownRenderer} from '@/common/components';

const privacyPolicyContent = `# 隐私政策

## 1. 信息收集
我们可能会收集以下类型的信息：
- 个人身份信息（如姓名、电子邮件地址）
- 使用数据（如访问时间、浏览页面）

## 2. 信息使用
我们收集的信息可能用于：
- 提供和改进服务
- 发送重要通知
`;

export default function PrivacyPolicy() {
    const {colorScheme} = useMantineColorScheme();
    // 处理 auto 情况，获取实际主题
    const actualColorScheme = colorScheme === 'auto' ?
        window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
        : colorScheme;

    const bgColor = actualColorScheme === 'dark' ? 'var(--mantine-color-dark-6)' : 'var(--mantine-color-gray-1)';
  return (
    <Container size="lg" py="xl" bg={bgColor} className="markdown-body">
      <MarkdownRenderer content={privacyPolicyContent} />
    </Container>
  );

}
