import {Button, Container, Input, PasswordInput, Stack, Text, Title} from '@mantine/core';
import {useForm, zodResolver} from '@mantine/form';
import {notifications} from '@mantine/notifications';
import {useMutation} from '@tanstack/react-query';
import {useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {resetPassword, sendVerificationCode} from '../api';
import {ResetPasswordRequestSchema} from '../types';

const ForgetPassword = () => {
    const navigate = useNavigate();
    const [countdown, setCountdown] = useState(0);

    const form = useForm({
        initialValues: {
            phone: '',
            code: '',
            new_password: ''
        },
        validate: zodResolver(ResetPasswordRequestSchema),
    });

    const sendCodeMutation = useMutation({
        mutationFn: sendVerificationCode,
        onSuccess: (response) => {
            if (response.error) {
                notifications.show({
                    title: '发送失败',
                    message: response.error.message,
                    color: 'red',
                });
            } else {
                notifications.show({
                    title: '发送成功',
                    message: '验证码已发送，请注意查收',
                    color: 'green',
                });
                setCountdown(60);
                const timer = setInterval(() => {
                    setCountdown((prev) => (prev > 0 ? prev - 1 : 0));
                }, 1000);
                setTimeout(() => clearInterval(timer), 60000);
            }
        },
        onError: (error) => {
            notifications.show({
                title: '发送失败',
                message: error.message,
                color: 'red',
            });
        }
    });

    const resetPasswordMutation = useMutation({
        mutationFn: resetPassword,
        onSuccess: (response) => {
            if (response.data) {
                notifications.show({
                    title: '密码重置成功',
                    message: '即将跳转到登录页面',
                    color: 'green',
                });
                navigate('/login');
            } else if (response.error) {
                notifications.show({
                    title: response.error.type,
                    message: response.error.message,
                    color: 'red',
                });
            }
        },
        onError: (error) => {
            notifications.show({
                title: '重置失败',
                message: error.message,
                color: 'red',
            });
        }
    });

    const handleSendCode = () => {
        const phoneError = form.validateField('phone').hasError;
        if (phoneError || !form.values.phone) return;
        sendCodeMutation.mutate(form.values.phone);
    };

    const handleSubmit = async () => {
        const result = form.validate();
        if (result.hasErrors) return;

        await resetPasswordMutation.mutateAsync({
            phone: form.values.phone,
            code: form.values.code,
            new_password: form.values.new_password
        });
        form.reset();
    };

    return (
        <Container size={600} my={80} style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <Title ta="center" className="title" style={{ fontSize: '2.5rem' }}>
                忘记密码
            </Title>
            <form>
                <Stack
                    className='relative flex-auto'
                    h="100%"
                    p="40px 64px 0"
                    align="stretch"
                    justify="flex-start"
                >
                    <Stack
                        align="stretch"
                        justify="flex-start"
                        gap="12px"
                    >
                        <Input.Wrapper error={form.errors.phone}>
                            <Input
                                {...form.getInputProps('phone')}
                                radius={8}
                                placeholder="请输入手机号"
                                rightSection={form.values.phone && <Input.ClearButton onClick={() => form.setFieldValue('phone', '')} />}
                                rightSectionPointerEvents="auto"
                                styles={{
                                    wrapper: {
                                        '--input-height': '40px'
                                    },
                                }}
                            />
                        </Input.Wrapper>

                        <Input.Wrapper error={form.errors.code}>
                            <Input
                                {...form.getInputProps('code')}
                                radius={8}
                                placeholder="短信验证码"
                                rightSection={
                                    <Text
                                        className={`cursor-pointer ${countdown > 0 ? 'text-gray-400' : 'hover:text-blue-500'}`}
                                        fz={14}
                                        onClick={handleSendCode}
                                    >
                                        {countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}
                                    </Text>
                                }
                                rightSectionWidth={100}
                                rightSectionPointerEvents="auto"
                                styles={{
                                    wrapper: {
                                        '--input-height': '40px',
                                        '--text-color': '#326BFF',
                                    },
                                }}
                            />
                        </Input.Wrapper>

                        <PasswordInput
                            {...form.getInputProps('new_password')}
                            radius={8}
                            placeholder="请输入新密码"
                            error={form.errors.new_password}
                            styles={{
                                input: {
                                    height: '40px'
                                },
                            }}
                        />
                    </Stack>

                    <Stack
                        align="stretch"
                        justify="flex-start"
                        gap="16px"
                    >
                        <Button
                            variant="gradient"
                            gradient={{ from: 'rgba(86, 104, 255, 1)', to: 'rgba(36, 162, 254, 1)', deg: 90 }}
                            radius={8}
                            fullWidth
                            onClick={handleSubmit}
                            loading={resetPasswordMutation.isPending}
                            styles={{
                                root: {
                                    '--button-height': '40px'
                                },
                            }}
                        >
                            重置密码
                        </Button>
                    </Stack>
                </Stack>
            </form>
        </Container>
    )
}

export default ForgetPassword;
