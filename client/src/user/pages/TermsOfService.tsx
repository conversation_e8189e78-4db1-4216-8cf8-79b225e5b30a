import {Container, useMantineColorScheme} from '@mantine/core';
import {MarkdownRenderer} from '@/common/components';

const termsContent = `# 用户服务协议

## 1. 协议概述

### 1.1 协议范围
本协议是您与我们之间关于使用我们的产品和服务所订立的协议。

### 1.2 协议效力
您通过网络页面点击确认或以其他方式选择接受本协议，即表示您同意接受本协议的全部内容。

## 2. 服务内容

### 2.1 服务定义
我们提供的服务包括但不限于：
- 基础功能服务
- 技术支持服务
- 其他增值服务

### 2.2 服务变更
我们保留随时修改或中断服务的权利，且无需对您或第三方负责。

## 3. 用户权利与义务

### 3.1 账号注册
- 您需提供真实、准确的个人资料
- 保护账号安全，不得将账号转让或出租
- 对账号下的所有行为负责

### 3.2 使用规范
您同意：
- 遵守相关法律法规
- 不传播违法违规内容
- 不从事危害网络安全的行为
- 不侵犯他人知识产权

## 4. 隐私保护

### 4.1 信息收集
我们将收集：
- 注册信息
- 使用记录
- 设备信息

### 4.2 信息使用
我们承诺：
- 依法保护用户隐私
- 不对外公开或提供用户个人信息
- 采取合理措施保护信息安全

## 5. 知识产权

### 5.1 所有权声明
- 平台内容的知识产权归我们所有
- 用户创作内容的权利归属按具体规则执行

### 5.2 授权范围
用户同意授予平台：
- 使用用户创作内容的权利
- 对内容进行传播的权利

## 6. 免责声明

### 6.1 服务免责
我们不承担因以下原因导致的责任：
- 系统维护或升级
- 通讯网络故障
- 不可抗力因素

### 6.2 内容免责
对于用户发布的内容：
- 不保证其准确性和合法性
- 不承担任何法律责任

## 7. 协议修改

### 7.1 修改权限
我们保留随时修改本协议的权利。

### 7.2 通知方式
- 在网站公告
- 发送电子邮件
- 其他合理方式

## 8. 协议终止

### 8.1 终止情形
- 用户主动注销
- 违反协议被终止
- 服务停止运营

### 8.2 终止后效力
- 清理用户数据
- 相关条款继续有效

## 9. 法律适用

### 9.1 适用法律
本协议适用中华人民共和国法律。

### 9.2 争议解决
发生争议时，双方应友好协商解决。

## 10. 其他条款

### 10.1 完整协议
本协议构成双方对本服务的完整约定。

### 10.2 条款独立性
如本协议某条款无效，不影响其他条款的效力。

## 11. 联系方式

如有任何问题，请通过以下方式联系我们：
- 客服电话：[电话号码]
- 电子邮箱：[邮箱地址]
- 办公地址：[详细地址]

最后更新日期：[日期]
`;

export default function TermsOfService() {
  const { colorScheme } = useMantineColorScheme();
  // 处理 auto 情况，获取实际主题
  const actualColorScheme = colorScheme === 'auto' ?
    window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    : colorScheme;

  const bgColor = actualColorScheme === 'dark' ? 'var(--mantine-color-dark-6)' : 'var(--mantine-color-gray-1)';
  return (
    <Container size="lg" py="xl" bg={bgColor} className="markdown-body">
      <MarkdownRenderer content={termsContent} />
    </Container>
  );
}
