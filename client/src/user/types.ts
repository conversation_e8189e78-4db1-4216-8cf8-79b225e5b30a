import { AgentType } from '@/agents';
import { z } from 'zod';

/**
 * 用户信息
 *
 * @property {number} id - 用户ID
 * @property {string} username - 用户名
 * @property {Date | null} [membership_expires] - 会员到期时间
 */
export interface UserInfo {
  id: number;
  username: string;
  can_create_agent: boolean;
}

/**
 * 登录结果
 *
 * @property {string} token - 登录令牌
 * @property {UserInfo} user_info - 用户信息
 */
export interface LoginResult {
  token: string;
  user_info: UserInfo;
}

export const RegisterRequestSchema = z.object({
  /**
   * 用户名，长度3-20个字符
   */
  username: z.string().min(3, '最小长度为3').max(20, '最大长度为20'),
  /**
   * 密码，至少6个字符，至少包含大小写字母和数字
   */
  password: z
    .string()
    .min(6, '最小长度为6')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{6,}$/,
      '必须包含大小写字母和数字'
    ),
  /**
   * 手机号，符合中国大陆手机号格式
   */
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
  /**
   * 验证码，6位数字
   */
  code: z.string().length(6, '请输入6位数字验证码')
});
export type RegisterRequest = z.infer<typeof RegisterRequestSchema>;

export type AssetType = 'course' | 'app';
export type PricingType = 'monthly' | 'yearly';

/**
 * 订单项
 *
 * @property {AssetType} asset_type - 资源类型
 * @property {string} asset_id - 资源ID
 * @property {number} quantity - 数量
 * @property {string} unit_price - 单价
 */
export interface OrderItem {
  asset_type: AssetType;
  asset_id: string;
  quantity: number;
  unit_price: string;
}

/**
 * 创建订单请求
 *
 * @property {PaymentMethod} payment_method - 支付方式，默认支付宝
 * @property {OrderItem[]} items - 订单项列表
 */
export interface OrderCreateRequest {
  payment_method: PaymentMethod;
  items: OrderItem[];
}
/**
 * 用户资产查询参数
 *
 * @property {AssetType} [assetType] - 资产类型
 * @property {string} [assetName] - 资产名称
 * @property {AgentType} [appMode] - 应用模式
 * @property {number} page - 页码，默认为1，最小值为1
 * @property {number} pageSize - 每页数量，默认为10，最小值为1，最大值为100
 * @property {boolean} [includeDetails] - 是否包含详情，默认为false
 */
export interface UserAssetQuery {
  assetType?: AssetType;
  assetName?: string;
  appMode?: AgentType;
  page: number;
  pageSize: number;
  includeDetails?: boolean;
}

export type PaymentMethod = 'alipay' | 'wechatpay';

/**
 * 订单结果
 *
 * @property {string} order_id - 订单ID
 * @property {string | null} payment_url - 支付链接
 * @property {string} message - 消息
 */
export interface OrderResult {
  order_id: string;
  payment_url?: string | null;
  message: string;
}
/**
 * 用户资产项
 *
 * @property {AssetType} asset_type - 资源类型
 * @property {number} asset_id - 资源ID
 * @property {Date} created_at - 创建时间
 */
export interface UserAssetItem {
  asset_type: AssetType;
  asset_id: string;
  asset_name: string;
  created_at: Date;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  details?: Record<string, any>; // 资产详情，根据类型不同结构不同
}
/**
 * 订单状态枚举
 */
export enum OrderStatus {
  /** 待支付 */
  PENDING = 'PENDING',
  /** 已支付 */
  PAID = 'PAID',
  /** 已取消 */
  CANCELLED = 'CANCELLED',
  /** 已退款 */
  REFUNDED = 'REFUNDED'
}

export interface Order {
  user_id: number;
  id: string;
  status: OrderStatus;
  pay_time: string;
  created_at: string;
  updated_at: string;
  amount: number;
  payment_method: PaymentMethod;
  items: OrderItem[];
}

export type UserAssetFilter = {
  label: string;
  value: string;
} & Omit<UserAssetQuery, 'page' | 'pageSize' | 'includeDetails' | 'assetName'>;

// 添加登录表单验证
export const LoginRequestSchema = z.object({
  username: z.string().min(3, '用户名至少3个字符'),
  password: z.string().min(6, '密码至少6个字符')
});
export type LoginRequest = z.infer<typeof LoginRequestSchema>;

export const SmsLoginRequestSchema = z.object({
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
  code: z.string().length(6, '请输入6位验证码')
});
export type SmsLoginRequest = z.infer<typeof SmsLoginRequestSchema>;

export const ResetPasswordRequestSchema = z.object({
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
  code: z.string().length(6, '请输入6位验证码'),
  new_password: z
    .string()
    .min(6, '最小长度为6')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{6,}$/,
      '必须包含大小写字母和数字'
    )
});
export type ResetPasswordRequest = z.infer<typeof ResetPasswordRequestSchema>;

/**
 * 用户账户信息
 *
 * @property {number} balance - 账户余额
 * @property {number} remaining_experience_count - 剩余体验次数
 * @property {number} remaining_tokens - 剩余token数量
 * @property {string} currency - 货币类型
 */
export interface UserAccountInfo {
  balance: number;
  remaining_experience_count: number;
  remaining_tokens: number;
  currency: string;
}
