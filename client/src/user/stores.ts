import {create} from 'zustand'
import {createJSONStorage, devtools, persist} from 'zustand/middleware'
import {UserInfo} from './types'

interface UserState {
    token: string | null
    userInfo: UserInfo | null
    setToken: (token: string) => void
    setUserInfo: (userInfo: UserInfo) => void
    clearUser: () => void
}

export const useUserStore = create<UserState>()(
    devtools(
        persist(
            (set) => ({
                token: null,
                userInfo: null,
                setToken: (token) => set({ token }, false, 'setToken'),
                setUserInfo: (userInfo) => set({ userInfo }, false, 'setUserInfo'),
                clearUser: () => set({ token: null, userInfo: null }, false, 'clearUser'),
            }),
            {
                name: 'user-storage',
                storage: createJSONStorage(() => localStorage),
            }
        ),
        {
            name: 'User Store',
        }
    )
)
