import React from 'react';
import { Container, Box, Text, Stack, Group, Avatar, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { RiRobot2Line, Ri<PERSON><PERSON><PERSON><PERSON>, RiBookLine } from 'react-icons/ri';
import { thousandChar } from '~/core/utils';

import { ModuleTitle } from '../components';

const Overview = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const bgColor = isDark ? theme.colors.dark[7] : 'white';
  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';

  return (
    <Container size="1176px" pt={72} pb={72}>
      <ModuleTitle title="我已购买" />

      <Group mt={24} mb={48} gap={32}>
        <Group
          className="rounded-[14px]"
          align="center"
          justify="space-between"
          w={360}
          h={130}
          pl={24}
          pr={20}
          py={24}
          bg={bgColor}
        >
          <Stack gap={8}>
            <Text fz={24} fw={700} lh="34px" c={textColor}>
              {thousandChar(1390)}
            </Text>
            <Text fz={22} fw={500} lh="32px" c={textColor}>
              已购买的智能体
            </Text>
          </Stack>
          <Avatar size={76} bg="rgba(73, 81, 235, 1)" color="white">
            <RiRobot2Line size={32} />
          </Avatar>
        </Group>

        <Group
          className="rounded-[14px]"
          align="center"
          justify="space-between"
          w={360}
          h={130}
          pl={24}
          pr={20}
          py={24}
          bg={bgColor}
        >
          <Stack gap={8}>
            <Text fz={24} fw={700} lh="34px" c={textColor}>
              {thousandChar(1390)}
            </Text>
            <Text fz={22} fw={500} lh="32px" c={textColor}>
              已购买的工作流
            </Text>
          </Stack>
          <Avatar size={76} bg="rgba(73, 81, 235, 1)" color="white">
            <RiFlowChart size={32} />
          </Avatar>
        </Group>

        <Group
          className="rounded-[14px]"
          align="center"
          justify="space-between"
          w={360}
          h={130}
          pl={24}
          pr={20}
          py={24}
          bg={bgColor}
        >
          <Stack gap={8}>
            <Text fz={24} fw={700} lh="34px" c={textColor}>
              {thousandChar(1390)}
            </Text>
            <Text fz={22} fw={500} lh="32px" c={textColor}>
              已购买的课程
            </Text>
          </Stack>
          <Avatar size={76} bg="rgba(73, 81, 235, 1)" color="white">
            <RiBookLine size={32} />
          </Avatar>
        </Group>
      </Group>

      <ModuleTitle title="资产" />

      <Box className="w-full rounded-[14px]" h={630} mt={24} bg={bgColor}></Box>
    </Container>
  );
};

export default Overview;
