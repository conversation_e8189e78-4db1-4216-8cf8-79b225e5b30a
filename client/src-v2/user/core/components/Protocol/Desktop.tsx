/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import { Text, useMantineTheme } from '@mantine/core';
import { Link } from 'react-router-dom';
import { useTheme } from '~/core/features/mantine/theme-context';

const Desktop = () => {
  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();
  const isDark = actualColorScheme === 'dark';

  const textColor = isDark ? theme.colors.gray[6] : 'rgba(166, 166, 166, 1)';
  const linkColor = 'rgba(70, 92, 231, 1)';

  return (
    <Text fz={12} fw={400} lh="14px" c={textColor}>
      同意并接受{' '}
      <Link
        to="/terms"
        style={{ color: linkColor, textDecoration: 'none' }}
        css={css`
          &:hover {
            color: rgba(112, 0, 254, 1) !important;
          }
        `}
      >
        《用户协议》
      </Link>
      和
      <Link
        to="/privacy"
        style={{ color: linkColor, textDecoration: 'none' }}
        css={css`
          &:hover {
            color: rgba(112, 0, 254, 1) !important;
          }
        `}
      >
        《隐私政策》
      </Link>
    </Text>
  );
};

export default Desktop;
