import { useState } from 'react';
import { useForm, zodResolver } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { RegisterRequest, RegisterRequestSchema } from '../schema';
import { register, sendVerificationCode } from '../api';
import { useUserStore } from '~/user/core/store';

export type RegisterFormValues = RegisterRequest;

/**
 * Custom hook for register functionality
 */
export const useRegister = () => {
  const navigate = useNavigate();
  const [countdown, setCountdown] = useState(0);

  // Form setup
  const form = useForm<RegisterFormValues>({
    initialValues: {
      username: '',
      phone: '',
      password: '',
      code: '',
      agreed: false,
    },
    validate: zodResolver(RegisterRequestSchema),
  });

  // Register mutation
  const registerMutation = useMutation({
    mutationFn: (values: RegisterFormValues) => register(values),
    onSuccess: (response) => {
      if (response.data) {
        notifications.show({
          title: '注册成功',
          message: '即将跳转到首页',
          color: 'green',
        });
        // 设置token和用户信息
        const { setToken, setUserInfo } = useUserStore.getState();
        setToken(response.data.token);
        setUserInfo(response.data.user_info);
        navigate('/');
      } else if (response.error) {
        notifications.show({
          title: '注册失败',
          message: response.error.message,
          color: 'red',
        });
      }
    },
    onError: (error: Error) => {
      notifications.show({
        title: '注册失败',
        message: error.message,
        color: 'red',
      });
    },
  });

  // Send verification code mutation
  const sendCodeMutation = useMutation({
    mutationFn: (phone: string) => sendVerificationCode(phone),
    onSuccess: (response) => {
      if (response.error) {
        notifications.show({
          title: '发送失败',
          message: response.error.message,
          color: 'red',
        });
      } else {
        notifications.show({
          title: '发送成功',
          message: '验证码已发送到您的手机',
          color: 'green',
        });

        // Start countdown
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      }
    },
    onError: (error: Error) => {
      notifications.show({
        title: '发送失败',
        message: error.message,
        color: 'red',
      });
    },
  });

  // Handle send verification code
  const handleSendCode = () => {
    const { phone } = form.values;
    const phoneError = form.validateField('phone').hasError;

    if (phoneError || !phone) {
      return;
    }

    sendCodeMutation.mutate(phone);
  };

  // Handle register form submission
  const handleRegister = (values: RegisterFormValues) => {
    form.validate();
    if (form.isValid()) {
      registerMutation.mutate(values);
    }
  };

  return {
    form,
    countdown,
    registerMutation,
    sendCodeMutation,
    handleSendCode,
    handleRegister,
  };
};

export default useRegister;
