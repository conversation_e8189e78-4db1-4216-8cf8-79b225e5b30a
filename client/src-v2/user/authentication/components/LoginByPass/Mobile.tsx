import { Button, Checkbox, Input, PasswordInput, Stack } from '@mantine/core';
import { useLogin } from '../../hooks';
import { Protocol } from '~/user/core';

const Mobile = () => {
  const { passwordForm, handleLogin, isLoading } = useLogin();

  return (
    <form onSubmit={passwordForm.onSubmit(handleLogin)}>
      <Stack gap="md">
        <Input
          placeholder="请输入用户名"
          key={passwordForm.key('username')}
          {...passwordForm.getInputProps('username')}
          rightSection={
            passwordForm.values.username !== '' ? (
              <Input.ClearButton onClick={() => passwordForm.setFieldValue('username', '')} />
            ) : undefined
          }
          rightSectionPointerEvents="auto"
        />

        <PasswordInput placeholder="请输入密码" key={passwordForm.key('password')} {...passwordForm.getInputProps('password')} />

        <Checkbox label={<Protocol />} mt={10} {...passwordForm.getInputProps('agreed')} size="xs" />

        <Button variant="filled" color="blue" type="submit" loading={isLoading} fullWidth mt="md">
          登录
        </Button>
      </Stack>
    </form>
  );
};

export default Mobile;
