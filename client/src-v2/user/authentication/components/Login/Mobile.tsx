import { Container, SegmentedControl, Stack, Text } from '@mantine/core';
import { useLogin } from '../../hooks';
import LoginByCode from '../LoginByCode';
import LoginByPass from '../LoginByPass';
import AccountOptions from '../AccountOptions';

const Mobile = () => {
  const { loginMethod, setLoginMethod } = useLogin();

  return (
    <Container size="xs" px="xs">
      <Stack gap="md" mt={30}>
        <Text fz={24} fw={600} ta="center">
          登录
        </Text>

        <SegmentedControl
          value={loginMethod}
          onChange={(value) => setLoginMethod(value as 'sms' | 'account')}
          data={[
            { label: '短信登录', value: 'sms' },
            { label: '账号登录', value: 'account' },
          ]}
          fullWidth
        />

        {loginMethod === 'sms' ? <LoginByCode /> : <LoginByPass />}

        {/* 使用新的 AccountOptions 组件 */}
        <AccountOptions scene="login" loginMethod={loginMethod} />
      </Stack>
    </Container>
  );
};

export default Mobile;
