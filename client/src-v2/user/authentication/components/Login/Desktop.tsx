/** @jsxImportSource @emotion/react */
import { Group, Container, SegmentedControl, Stack, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';

import { useLogin } from '../../hooks';
import AuthTitle from '../AuthTitle';
import LoginByCode from '../LoginByCode';
import LoginByPass from '../LoginByPass';
import AccountOptions from '../AccountOptions';

const Desktop = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const { loginMethod, setLoginMethod, title } = useLogin();

  const containerBgColor = isDark ? theme.colors.dark[8] : 'rgba(243, 245, 255, 1)';
  const loginTypeLabelColor = isDark ? 'rgba(70, 92, 231, 1)' : 'rgba(73, 81, 235, 1)';

  return (
    <Container className="flex justify-center" my={112}>
      <Stack align="center" w={664} pt={80} pb={72} bg={containerBgColor} gap={0}>
        <AuthTitle title={title} />

        <Stack className="w-full" px={56} align="stretch" justify="center" gap={20}>
          <Group align="center" justify="center" mt={12}>
            <SegmentedControl
              value={loginMethod}
              onChange={(value) => setLoginMethod(value as 'sms' | 'account')}
              data={[
                { label: '短信登录', value: 'sms' },
                { label: '账号登录', value: 'account' },
              ]}
              styles={{
                root: {
                  '--sc-label-color': loginTypeLabelColor,
                  '--sc-font-size': '18px',
                  width: '260px',
                  backgroundColor: 'transparent',
                },
                label: {
                  width: '124px',
                  height: '30px',
                  lineHeight: '30px',
                  padding: '0',
                },
              }}
            />
          </Group>

          {loginMethod === 'sms' ? <LoginByCode /> : <LoginByPass />}

          {/* 使用新的 AccountOptions 组件 */}
          <AccountOptions scene="login" loginMethod={loginMethod} />
        </Stack>
      </Stack>
    </Container>
  );
};

export default Desktop;
