import React from 'react';
import { Divider, Text, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';

import { AuthTitleProps } from './types';

const Desktop: React.FC<AuthTitleProps> = ({ title }) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const titleBgColor = isDark ? 'white' : 'black';
  const dividerColor = isDark ? theme.colors.gray[8] : 'rgba(219, 225, 255, 1)';

  return (
    <>
      <Text fz={36} fw={500} lh="52px" c={titleBgColor}>
        {title}
      </Text>

      <Divider w="100%" mt={40} color={dividerColor} />
    </>
  );
};

export default Desktop;
