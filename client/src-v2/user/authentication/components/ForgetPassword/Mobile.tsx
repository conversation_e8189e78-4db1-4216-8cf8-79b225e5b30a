import React from 'react';
import { Button, Checkbox, Container, Input, PasswordInput, Stack, Text, Title } from '@mantine/core';
import { useForgetPassword } from '../../hooks';
import { Protocol } from '~/user/core';
import AccountOptions from '../AccountOptions';

const Mobile = () => {
  // 使用忘记密码Hook
  const { form, countdown, handleSendCode, handleResetPassword, resetPasswordMutation } = useForgetPassword();

  const ProtocolLink = <Protocol />;

  return (
    <Container size="xs" px="xs">
      <Stack gap="md" mt={30}>
        <Title order={2} ta="center" mb={20} fw={500} fz={28}>
          忘记密码
        </Title>

        <form>
          <Stack gap="md">
            <Input.Wrapper label="手机号" error={form.errors.phone}>
              <Input
                {...form.getInputProps('phone')}
                placeholder="请输入手机号"
                rightSection={
                  form.values.phone ? <Input.ClearButton onClick={() => form.setFieldValue('phone', '')} /> : undefined
                }
                rightSectionPointerEvents="auto"
              />
            </Input.Wrapper>

            <Input.Wrapper label="验证码" error={form.errors.code}>
              <Input
                {...form.getInputProps('code')}
                placeholder="短信验证码"
                rightSection={
                  <Text
                    className={`cursor-pointer ${countdown > 0 ? 'text-gray-400' : ''}`}
                    fz={14}
                    onClick={countdown > 0 ? undefined : handleSendCode}
                    c={countdown > 0 ? 'gray.5' : 'blue'}
                  >
                    {countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}
                  </Text>
                }
                rightSectionWidth={100}
                rightSectionPointerEvents="auto"
              />
            </Input.Wrapper>

            <PasswordInput {...form.getInputProps('new_password')} label="设置密码" placeholder="请输入新密码" />

            <Checkbox {...form.getInputProps('agreed', { type: 'checkbox' })} mt="md" label={ProtocolLink} />

            <Button fullWidth mt="md" onClick={handleResetPassword} loading={resetPasswordMutation.isPending}>
              重置密码
            </Button>
          </Stack>
        </form>

        {/* 使用新的 AccountOptions 组件 */}
        <AccountOptions scene="forgetPassword" />
      </Stack>
    </Container>
  );
};

export default Mobile;
