import React from 'react';
import { Group, Text } from '@mantine/core';
import { Link } from 'react-router-dom';
import { AccountOptionsProps } from './types';

/**
 * 移动端账户选项组件
 */
const Mobile: React.FC<AccountOptionsProps> = ({ scene, loginMethod }) => {
  // 根据场景和登录方式渲染不同的内容
  if (scene === 'login' && loginMethod === 'account') {
    // 账号密码登录时显示"没有账号?立即注册"和"忘记密码?"两个选项，一左一右
    return (
      <Group position="apart" mt={20} mb={10} w="100%">
        <Link to="/register" style={{ color: '#465CE7', textDecoration: 'none' }}>
          没有账号？立即注册
        </Link>
        <Link to="/forget-password" style={{ color: '#465CE7', textDecoration: 'none' }}>
          忘记密码？
        </Link>
      </Group>
    );
  } else if (scene === 'login' && loginMethod === 'sms') {
    // 短信登录时不显示"忘记密码"
    return null;
  } else if (scene === 'register' || scene === 'forgetPassword') {
    // 注册和忘记密码页面显示"已有账号，立即登录"
    return (
      <Group align="center" justify="center" gap={5} mt={20} mb={10}>
        <Text fz={14} c="dimmed">
          已有账号？
        </Text>
        <Link to="/login" style={{ color: '#465CE7', textDecoration: 'none' }}>
          立即登录
        </Link>
      </Group>
    );
  }

  return null;
};

export default Mobile;
