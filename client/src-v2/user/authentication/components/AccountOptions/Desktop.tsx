/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import { Group, Text, UnstyledButton } from '@mantine/core';
import { Link } from 'react-router-dom';
import { AccountOptionsProps } from './types';

/**
 * 桌面端账户选项组件
 */
const Desktop: React.FC<AccountOptionsProps> = ({ scene, loginMethod }) => {
  // 链接样式
  const linkStyle = css`
    &:hover {
      color: rgba(112, 0, 254, 1) !important;
    }
  `;

  // 根据场景和登录方式渲染不同的内容
  if (scene === 'login' && loginMethod === 'account') {
    // 账号密码登录时显示"没有账号?立即注册"和"忘记密码?"两个选项，一左一右
    return (
      <Group position="apart" justify="space-between" mt={44} w="100%">
        <Group align="center" gap={0}>
          <Text fz={16} fw={400} lh="24px" c="rgba(128, 128, 128, 1)">
            没有有账号？
          </Text>
          <UnstyledButton component={Link} to="/register">
            <Text fz={16} fw={400} lh="24px" c="rgba(70, 92, 231, 1)" css={linkStyle}>
              立即注册
            </Text>
          </UnstyledButton>
        </Group>

        <UnstyledButton component={Link} to="/forget-password">
          <Text fz={16} fw={400} lh="24px" c="rgba(70, 92, 231, 1)" css={linkStyle}>
            忘记密码？
          </Text>
        </UnstyledButton>
      </Group>
    );
  } else if (scene === 'login' && loginMethod === 'sms') {
    // 短信登录时不显示"忘记密码"，因为短信登录本身就是找回密码的一种方式
    return null;
  } else if (scene === 'register' || scene === 'forgetPassword') {
    // 注册和忘记密码页面显示"已有账号，立即登录"
    return (
      <Group align="center" justify="center" gap={0} mt={44}>
        <Text fz={16} fw={400} lh="24px" c="rgba(128, 128, 128, 1)">
          已有账号？
        </Text>
        <UnstyledButton component={Link} to="/login">
          <Text fz={16} fw={400} lh="24px" c="rgba(70, 92, 231, 1)" css={linkStyle}>
            立即登录
          </Text>
        </UnstyledButton>
      </Group>
    );
  }

  return null;
};

export default Desktop;
