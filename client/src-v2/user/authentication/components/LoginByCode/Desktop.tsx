/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import { Button, Checkbox, Input, Text, Stack } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { useMutation } from '@tanstack/react-query';
import { sendVerificationCode } from '~/user/authentication/api';
import { notifications } from '@mantine/notifications';
import { useLogin } from '../../hooks';
import { Protocol } from '~/user/core';

const Desktop = () => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const { smsForm, countdown, setCountdown, handleLogin, isLoading } = useLogin();

  const labelTextColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';

  // Send verification code mutation
  const sendCodeMutation = useMutation({
    mutationFn: sendVerificationCode,
    onSuccess: (response) => {
      if (response.error) {
        notifications.show({
          title: '发送失败',
          message: response.error.message,
          color: 'red',
        });
      } else {
        notifications.show({
          title: '发送成功',
          message: '验证码已发送，请注意查收',
          color: 'green',
        });
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown((prev) => (prev > 0 ? prev - 1 : 0));
        }, 1000);
        setTimeout(() => clearInterval(timer), 60000);
      }
    },
    onError: (error: Error) => {
      notifications.show({
        title: '发送失败',
        message: error.message,
        color: 'red',
      });
    },
  });

  const sendSmsCode = () => {
    if (countdown > 0) return;
    const phoneError = smsForm.validateField('phone').hasError;
    if (phoneError || !smsForm.values.phone) {
      notifications.show({
        title: '发送失败',
        message: '请输入正确的手机号',
        color: 'red',
      });
      return;
    }
    sendCodeMutation.mutate(smsForm.values.phone);
  };

  return (
    <form onSubmit={smsForm.onSubmit(handleLogin)}>
      <Stack gap="md">
        <Input.Wrapper
          label="手机号"
          styles={{
            label: {
              fontSize: 16,
              fontWeight: 400,
              lineHeight: '18px',
              color: labelTextColor,
            },
          }}
        >
          <Input
            radius={8}
            placeholder="请输入手机号"
            key={smsForm.key('phone')}
            {...smsForm.getInputProps('phone')}
            styles={{
              wrapper: {
                '--input-fz': '16px',
                marginTop: 8,
              },
              input: {
                height: 52,
                paddingLeft: 15,
                paddingRight: 15,
                backgroundColor: 'white',
                borderRadius: 6,
              },
            }}
          />
        </Input.Wrapper>

        <Input.Wrapper
          label="验证码"
          styles={{
            label: {
              fontSize: 16,
              fontWeight: 400,
              lineHeight: '18px',
              color: labelTextColor,
            },
          }}
        >
          <Input
            placeholder="短信验证码"
            key={smsForm.key('code')}
            {...smsForm.getInputProps('code')}
            rightSection={
              <Text
                className={`cursor-pointer ${countdown > 0 ? 'text-gray-400' : ''}`}
                fz={16}
                fw={400}
                lh="24px"
                c={countdown > 0 ? 'rgba(166, 166, 166, 1)' : 'rgba(70, 92, 231, 1)'}
                onClick={sendSmsCode}
                css={css`
                  &:hover {
                    color: ${countdown > 0 ? 'rgba(166, 166, 166, 1)' : 'rgba(112, 0, 254, 1)'} !important;
                  }
                `}
              >
                {countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}
              </Text>
            }
            rightSectionWidth={100}
            rightSectionPointerEvents="auto"
            styles={{
              wrapper: {
                '--input-fz': '16px',
                marginTop: 8,
              },
              input: {
                height: 52,
                paddingLeft: 15,
                paddingRight: 15,
                backgroundColor: 'white',
                borderRadius: 6,
              },
              section: {
                '--section-end': '15px',
                justifyContent: 'flex-end',
              },
            }}
          />
        </Input.Wrapper>
        <Checkbox
          label={<Protocol />}
          mt={20}
          {...smsForm.getInputProps('agreed')}
          color="rgba(73, 81, 235, 1)"
          styles={{
            root: {
              '--checkbox-radius': '2px',
              '--checkbox-size': '14px',
            },
            body: {
              display: 'flex',
              alignItems: 'center',
            },
            label: {
              paddingLeft: '8px',
            },
          }}
        />

        <Button
          variant="filled"
          mt={20}
          color="rgba(70, 92, 231, 1)"
          type="submit"
          loading={isLoading}
          styles={{
            root: {
              '--button-height': '52px',
              '--button-fz': '16px',
            },
          }}
        >
          登录
        </Button>
      </Stack>
    </form>
  );
};

export default Desktop;
