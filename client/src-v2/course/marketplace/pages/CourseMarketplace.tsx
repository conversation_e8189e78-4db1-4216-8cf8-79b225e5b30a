import React, { useState, useRef, useEffect } from 'react';
import { Container, Space, Stack, Group, Box, useMantineTheme, Text, Skeleton } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { useDeviceDetect, useScrollSpy } from '~/core/hooks';
import { ModuleTitle, SearchBox, FilterTabs, CategoryTitle, ModuleList, ModuleCard, LoadMore } from '~/core/components';
import { useCourseMarketplace } from '../hooks';
import { Course } from '../schemas';

/**
 * CourseMarketplace组件 - 展示AI课程市场页面
 *
 * 该组件实现了一个分类展示的课程市场，包含以下功能：
 * 1. 顶部搜索框用于搜索课程
 * 2. 分类标签栏用于快速导航到不同类别
 * 3. 按类别分组展示课程卡片
 * 4. 支持滚动时自动更新当前活动分类
 * 5. 响应式布局适配移动端和桌面端
 * 6. 加载更多功能用于分页加载内容
 */
const CourseMarketplace: React.FC = () => {
  // 获取Mantine主题和颜色方案
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  // 检测设备类型，用于响应式布局
  const { isMobile } = useDeviceDetect();

  // 根据当前主题设置粘性导航栏背景色
  const stickyBgColor = isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)';

  // 使用课程商店钩子获取数据和状态
  const {
    tags,
    courses,
    isCoursesLoading,
    coursesError,
    handleCategoryChange: storeHandleCategoryChange,
    searchKeyword,
    searchResult,
    isSearchLoading,
    searchError,
    research,
    setSearchKeyword,
    loadMore,
    loadingCategories,
  } = useCourseMarketplace();

  // 将标签转换为分类数据格式
  const categories = tags.map((tag) => ({
    title: tag,
    subtitle: `${tag}分类下的课程`,
  }));

  // 如果没有分类数据，添加默认分类
  if (categories.length === 0) {
    categories.push({
      title: '精选推荐',
      subtitle: 'AI资讯‌是指关于人工智能的最新动态、研究成果、技术进展和应用案例等信息',
    });
  }

  // 组件状态管理
  const [loadMoreDisabledMap, setLoadMoreDisabledMap] = useState<Record<string, boolean>>({}); // 每个分类的加载更多禁用状态
  const [tagCoursesMap, setTagCoursesMap] = useState<Record<string, Course[]>>({}); // 每个分类的课程列表（包括加载更多的）

  // 初始化数据
  useEffect(() => {
    if (courses && courses.data) {
      // 初始化每个标签的禁用状态和课程列表
      const initialDisabledMap: Record<string, boolean> = {};
      const initialCoursesMap: Record<string, Course[]> = {};

      Object.keys(courses.data).forEach((tag) => {
        initialDisabledMap[tag] = false;
        initialCoursesMap[tag] = [...courses.data[tag]];
      });

      setLoadMoreDisabledMap(initialDisabledMap);
      setTagCoursesMap(initialCoursesMap);
    }
  }, [courses]);

  // Refs
  const containerRef = useRef<HTMLDivElement>(null); // 容器引用，用于滚动计算

  // 使用滚动监听钩子
  const { activeSection, selectedCategory, handleCategoryChange } = useScrollSpy({
    containerRef,
    items: categories,
    sectionAttribute: 'data-section',
    sectionIdPrefix: 'section-',
    stickyHeaderSelector: '.sticky',
    triggerOffset: 42,
    manualScrollTimeout: 1000,
  });

  // 当滚动监听钩子的分类变化时，同步到store
  useEffect(() => {
    if (selectedCategory) {
      storeHandleCategoryChange(selectedCategory.title);
    }
  }, [selectedCategory, storeHandleCategoryChange]);

  // 计算当前活动分类（基于滚动位置或用户选择）
  const activeCategory = categories.find((item) => item.title === activeSection) || selectedCategory;

  return (
    <Container size="720px" pt={80} ref={containerRef}>
      {/* 页面标题 */}
      <ModuleTitle title="AI课程" subtitle="探索各种精彩的AI课程，提升您的知识和技能" />

      <Space w={'100%'} h={24} />

      {/* 粘性导航区域：包含搜索框和分类标签 */}
      <Box className="sticky top-0 z-100" pt={16} bg={stickyBgColor}>
        {/* 搜索框 */}
        <SearchBox
          onSearch={research}
          searchKeyword={searchKeyword}
          isSearchLoading={isSearchLoading}
          searchError={searchError}
          searchResult={searchResult}
          setSearchKeyword={setSearchKeyword}
          placeholder="搜索课程..."
        />

        <Space h={40} />

        {/* 分类标签栏 */}
        <FilterTabs categories={categories} selectedCategory={activeCategory} onCategoryChange={handleCategoryChange} />

        {/* 加载状态提示 - 当加载课程数据时，分类也在加载中 */}
        {isCoursesLoading && (
          <Text c="dimmed" size="sm" ta="center" mt={10}>
            正在加载分类...
          </Text>
        )}
      </Box>

      {/* 加载状态显示 */}
      {isCoursesLoading && (
        <Stack gap="md" align="center" justify="center" py={80}>
          <Skeleton height={40} width="80%" radius="md" />
          <Skeleton height={200} width="100%" radius="md" />
          <Skeleton height={200} width="100%" radius="md" />
        </Stack>
      )}

      {/* 错误状态显示 */}
      {coursesError && (
        <Stack gap="md" align="center" justify="center" py={80}>
          <Text c="red" size="lg" ta="center">
            加载课程数据失败: {(coursesError as Error).message}
          </Text>
        </Stack>
      )}

      {/* 没有数据显示 */}
      {!isCoursesLoading && !coursesError && (!courses || Object.keys(courses.data || {}).length === 0) && (
        <Stack gap="md" align="center" justify="center" py={80}>
          <Text c="dimmed" size="lg" ta="center">
            暂无课程数据
          </Text>
        </Stack>
      )}

      {/* 动态生成分类区块 */}
      {!isCoursesLoading &&
        !coursesError &&
        courses &&
        courses.data &&
        Object.keys(courses.data).map((tag) => (
          <Stack gap={0} py={48} id={`section-${tag}`} data-section={tag} key={tag}>
            <CategoryTitle title={tag} subtitle={`${tag}分类下的课程`} />

            <Space h={16} />

            {/* 课程卡片列表 */}
            <ModuleList columns={isMobile ? 1 : 2} gap="md">
              {(tagCoursesMap[tag] || courses.data[tag] || []).map((course, index) => (
                <ModuleCard
                  key={course.id}
                  id={course.id}
                  index={index + 1}
                  name={course.name}
                  description={course.description ?? course.name}
                  iconUrl={course.icon || 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743067298-ai.jpg'}
                  emphasized={tag === '精选推荐'}
                  linkUrl={`/course/${course.id}`}
                />
              ))}
            </ModuleList>

            {/* 加载更多按钮 */}
            {tagCoursesMap[tag]?.length > 0 && (
              <Group align="center" justify="center" mt={40}>
                <LoadMore
                  text={loadMoreDisabledMap[tag] ? '没有更多内容' : '查看更多'}
                  onClick={async () => {
                    try {
                      // 获取当前分类已加载的课程ID列表
                      const existingIds = tagCoursesMap[tag].map((course) => course.id);

                      // 调用 loadMore 函数获取更多数据
                      const newCourses = await loadMore(tag, existingIds);

                      // 只有在没有更多数据时才禁用加载更多按钮
                      if (newCourses.length < 4) {
                        setLoadMoreDisabledMap((prev) => ({ ...prev, [tag]: true }));
                      }

                      // 更新当前分类的课程列表
                      setTagCoursesMap((prev) => ({
                        ...prev,
                        [tag]: [...prev[tag], ...newCourses],
                      }));
                    } catch (error) {
                      console.error(`加载更多课程失败: ${error}`);
                    }
                  }}
                  loading={loadingCategories[tag] || false}
                  disabled={loadMoreDisabledMap[tag] || false}
                />
              </Group>
            )}
          </Stack>
        ))}
    </Container>
  );
};

export default CourseMarketplace;
CourseMarketplace.displayName = 'CourseMarketplace';
