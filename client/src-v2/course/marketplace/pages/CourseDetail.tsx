import React, { useState } from 'react';
import { Container, Space, Text } from '@mantine/core';
import { CourseInfo, CourseView, FilterTabs, CourseIntro, CourseList, Comment, CommentInput, Recommend } from '../components';

import { CourseItem } from '../components/CourseList/types';

const CourseDetail: React.FC = () => {
  // 加入购物车
  const onCartClick = () => {
    console.log('onCartClick');
  };
  // 立即购买
  const onBuyClick = () => {
    console.log('onCartClick');
  };

  const tabs = [
    {
      label: '课程介绍',
      type: 'intro',
    },
    {
      label: '课程列表',
      type: 'list',
    },
    {
      label: '评论',
      type: 'comment',
    },
  ];
  const [selectedTab, setSelectedTab] = useState(tabs[0]);

  const courseList = [
    {
      id: '1',
      title: '第一讲：最新AI绘画系统教程（一）',
      duration: '1小时10分钟',
    },
    {
      id: '2',
      title: '第二讲：最新AI绘画系统教程（二）',
      duration: '1小时10分钟',
    },
    {
      id: '3',
      title: '第三讲：最新AI绘画系统教程（三）',
      duration: '1小时10分钟',
    },
    {
      id: '4',
      title: '第四讲：最新AI绘画系统教程（四）',
      duration: '1小时10分钟',
    },
    {
      id: '5',
      title: '第五讲：最新AI绘画系统教程（五）',
      duration: '1小时10分钟',
    },
    {
      id: '6',
      title: '第六讲：最新AI绘画系统教程（六）',
      duration: '1小时10分钟',
    },
    {
      id: '7',
      title: '第七讲：最新AI绘画系统教程（七）',
      duration: '1小时10分钟',
    },
    {
      id: '8',
      title: '第八讲：最新AI绘画系统教程（八）',
      duration: '1小时10分钟',
    },
    {
      id: '9',
      title: '第九讲：最新AI绘画系统教程（九）',
      duration: '1小时10分钟',
    },
    {
      id: '10',
      title: '第十讲：最新AI绘画系统教程（十）',
      duration: '1小时10分钟',
    },
    {
      id: '11',
      title: '第十讲：最新AI绘画系统教程（十）',
      duration: '1小时10分钟',
    },
    {
      id: '12',
      title: '第十讲：最新AI绘画系统教程（十）',
      duration: '1小时10分钟',
    },
  ];

  const commentList = [
    {
      id: '1',
      avatar: 'https://avatars.githubusercontent.com/u/115162199?s=400&u=490007c738c07594c6666568296167882768615d&v=4',
      name: 'John Doe',
      content: 'This is a comment',
      time: '2021-01-01',
      star: 5,
    },
    {
      id: '2',
      avatar: 'https://avatars.githubusercontent.com/u/115162199?s=400&u=490007c738c07594c6666568296167882768615d&v=4',
      name: 'John Doe',
      content: 'This is a comment',
      time: '2021-01-01',
      star: 4,
    },
    {
      id: '3',
      avatar: 'https://avatars.githubusercontent.com/u/115162199?s=400&u=490007c738c07594c6666568296167882768615d&v=4',
      name: 'John Doe',
      content: 'This is a comment',
      time: '2021-01-01',
      star: 3,
    },
    {
      id: '4',
      avatar: 'https://avatars.githubusercontent.com/u/115162199?s=400&u=490007c738c07594c6666568296167882768615d&v=4',
      name: 'John Doe',
      content: 'This is a comment',
      time: '2021-01-01',
      star: 2,
    },
    {
      id: '5',
      avatar: 'https://avatars.githubusercontent.com/u/115162199?s=400&u=490007c738c07594c6666568296167882768615d&v=4',
      name: 'John Doe',
      content: 'This is a comment',
      time: '2021-01-01',
      star: 1,
    },
  ];

  const recommendList = [
    {
      id: 7,
      linkUrl: '/course/7',
      title: '第七讲：最新AI绘画系统教程（七）',
      image: 'https://img.js.design/assets/img/6803b25b210a5c79b2473576.png#48643bcca7bfdc40446d892a27005668',
      price: 100,
      star: 5,
    },
    {
      id: 8,
      linkUrl: '/course/8',
      title: '第八讲：最新AI绘画系统教程（八）',
      image: 'https://img.js.design/assets/img/6803b25b210a5c79b2473576.png#48643bcca7bfdc40446d892a27005668',
      price: 100,
      star: 4,
    },
    {
      id: 9,
      linkUrl: '/course/9',
      title: '第九讲：最新AI绘画系统教程（九）',
      image: 'https://img.js.design/assets/img/6803b25b210a5c79b2473576.png#48643bcca7bfdc40446d892a27005668',
      price: 100,
      star: 3,
    },
    {
      id: 10,
      linkUrl: '/course/10',
      title: '第十讲：最新AI绘画系统教程（十）',
      image: 'https://img.js.design/assets/img/6803b25b210a5c79b2473576.png#48643bcca7bfdc40446d892a27005668',
      price: 100,
      star: 2,
    },
    {
      id: 17,
      linkUrl: '/course/17',
      title: '第七讲：最新AI绘画系统教程（七）',
      image: 'https://img.js.design/assets/img/6803b25b210a5c79b2473576.png#48643bcca7bfdc40446d892a27005668',
      price: 100,
      star: 5,
    },
    {
      id: 18,
      linkUrl: '/course/18',
      title: '第八讲：最新AI绘画系统教程（八）',
      image: 'https://img.js.design/assets/img/6803b25b210a5c79b2473576.png#48643bcca7bfdc40446d892a27005668',
      price: 100,
      star: 4,
    },
    {
      id: 19,
      linkUrl: '/course/19',
      title: '第九讲：最新AI绘画系统教程（九）',
      image: 'https://img.js.design/assets/img/6803b25b210a5c79b2473576.png#48643bcca7bfdc40446d892a27005668',
      price: 100,
      star: 3,
    },
    {
      id: 110,
      linkUrl: '/course/110',
      title: '第十讲：最新AI绘画系统教程（十）',
      image: 'https://img.js.design/assets/img/6803b25b210a5c79b2473576.png#48643bcca7bfdc40446d892a27005668',
      price: 100,
      star: 2,
    },
  ];

  const onCourseSectionClick = (item: CourseItem) => {
    console.log('onCourseSectionClick', item);
  };

  return (
    <Container size="1024px" pt={112}>
      <CourseInfo onCartClick={onCartClick} onBuyClick={onBuyClick} />

      <Space h={72} />

      <CourseView />

      <Space h={72} />

      <FilterTabs tabs={tabs} selected={selectedTab} onChange={setSelectedTab} />

      <Space h={28} />

      {selectedTab.type === 'intro' && <CourseIntro data="" />}
      {selectedTab.type === 'list' && <CourseList list={courseList} onClick={onCourseSectionClick} />}
      {selectedTab.type === 'comment' && (
        <>
          <Comment list={commentList} />
          <CommentInput />
        </>
      )}

      <Space h={56} />

      <Text className="leading-[30px] fw-500 text-center" fz={20} mb={24}>
        推荐好课
      </Text>
      <Recommend list={recommendList} />
    </Container>
  );
};

export default CourseDetail;
