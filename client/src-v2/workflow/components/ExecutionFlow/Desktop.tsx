import React from 'react';
import { Stack, Group, Text, Accordion, Avatar, useMantineTheme } from '@mantine/core';
import { Ri<PERSON>lowChart, RiCheckboxCircleFill, RiLinkM, RiErrorWarningFill, RiCloseCircleFill, RiFilmLine } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';

const Desktop = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const containerBgColor = isDark ? theme.colors.dark[9] : 'white';
  const itemBgColor = isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)';
  const panelItemBgColor = isDark ? theme.colors.dark[9] : 'white';

  return (
    <Stack
      className="flex-auto"
      w={800}
      p={64}
      style={{
        background: containerBgColor,
        boxShadow: '0px 2px 4px  rgba(0, 0, 0, 0.1)',
      }}
      gap={0}
    >
      <Accordion defaultValue="Apples">
        <Accordion.Item
          key="flow"
          value="flow"
          bg={itemBgColor}
          style={{
            borderColor: 'transparent',
          }}
        >
          <Accordion.Control>
            <Group gap={8}>
              <Avatar
                size={24}
                styles={{
                  placeholder: {
                    background: 'rgba(73, 81, 235, 1)',
                    color: 'white',
                  },
                }}
              >
                <RiFlowChart size={12} />
              </Avatar>
              <Text fz={12} fw={700} lh="18px">
                工作流
              </Text>
            </Group>
          </Accordion.Control>
          <Accordion.Panel>
            <Stack gap={10}>
              <Group h={36} px={16} bg={panelItemBgColor}>
                <Avatar
                  className="flex-none"
                  size={16}
                  styles={{
                    placeholder: {
                      background: 'rgba(73, 81, 235, 1)',
                      color: 'white',
                    },
                  }}
                >
                  <RiFlowChart size={8} />
                </Avatar>
                <Text className="flex-auto" fz={12} fw={500} lh="18px">
                  工作流
                </Text>
                <RiCheckboxCircleFill className="flex-none" size={16} color="rgba(165, 214, 63, 1)" />
              </Group>

              <Group h={36} px={16} bg={panelItemBgColor}>
                <Avatar
                  className="flex-none"
                  size={16}
                  styles={{
                    placeholder: {
                      background: 'rgba(73, 81, 235, 1)',
                      color: 'white',
                    },
                  }}
                >
                  <RiLinkM size={8} />
                </Avatar>
                <Text className="flex-auto" fz={12} fw={500} lh="18px">
                  提取真实的对标账号链接
                </Text>
                <RiErrorWarningFill className="flex-none" size={16} color="rgba(255, 235, 59, 1)" />
              </Group>

              <Group h={36} px={16} bg={panelItemBgColor}>
                <Avatar
                  className="flex-none"
                  size={16}
                  styles={{
                    placeholder: {
                      background: 'rgba(73, 81, 235, 1)',
                      color: 'white',
                    },
                  }}
                >
                  <RiFilmLine size={8} />
                </Avatar>
                <Text className="flex-auto" fz={12} fw={500} lh="18px">
                  视频采集
                </Text>
                <RiCloseCircleFill className="flex-none" size={16} color="rgba(212, 48, 48, 1)" />
              </Group>
            </Stack>
          </Accordion.Panel>
        </Accordion.Item>
      </Accordion>
    </Stack>
  );
};

export default Desktop;
