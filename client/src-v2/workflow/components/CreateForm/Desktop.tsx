import React, { useState } from 'react';
import { Stack, Group, Image, Text, Textarea, TextInput, Button, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { FilterTabs } from '../index';

import { FiPlayCircle } from 'react-icons/fi';

const Desktop = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const containerBgColor = isDark ? theme.colors.dark[9] : 'white';
  const labelTextColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';

  const tabs = [
    {
      label: 'Run Once',
      type: 'once',
    },
    {
      label: 'Run Batch',
      type: 'batch',
    },
  ];
  const [selectedTab, setSelectedTab] = useState(tabs[0]);

  const playIcon = <FiPlayCircle size={16} />;

  return (
    <Stack
      className="flex-none"
      w={600}
      p={64}
      style={{
        background: containerBgColor,
        boxShadow: '0px 2px 4px  rgba(0, 0, 0, 0.1)',
      }}
      gap={0}
    >
      <Group mb={24} gap={18}>
        <Image
          w={48}
          h={48}
          src="https://avatars.githubusercontent.com/u/115162199?s=400&u=490007c738c07594c6666568296167882768615d&v=4"
        />
        <Text fz={12} fw={400} lh="18px">
          抖音对标账号采集1
        </Text>
      </Group>

      <FilterTabs tabs={tabs} selected={selectedTab} onChange={setSelectedTab} />

      <Stack mt={20} mb={24} gap={16}>
        <Textarea
          variant="unstyled"
          label="分享链接"
          placeholder="分享链接（Optional）"
          styles={{
            label: {
              fontSize: 12,
              fontWeight: 400,
              lineHeight: '18px',
              color: labelTextColor,
            },
            wrapper: {
              marginTop: 8,
            },
            input: {
              height: 130,
              paddingLeft: 12,
              paddingRight: 12,
              backgroundColor: 'rgba(247, 248, 255, 1)',
            },
          }}
        />

        <TextInput
          variant="unstyled"
          label="抓取数量"
          placeholder="抓取数量（Optional）"
          styles={{
            label: {
              fontSize: 12,
              fontWeight: 400,
              lineHeight: '18px',
              color: labelTextColor,
            },
            wrapper: {
              marginTop: 8,
            },
            input: {
              height: 36,
              paddingLeft: 12,
              paddingRight: 12,
              backgroundColor: 'rgba(247, 248, 255, 1)',
            },
          }}
        />
        <TextInput
          variant="unstyled"
          label="关键字"
          placeholder="关键字（Optional）"
          styles={{
            label: {
              fontSize: 12,
              fontWeight: 400,
              lineHeight: '18px',
              color: labelTextColor,
            },
            wrapper: {
              marginTop: 8,
            },
            input: {
              height: 36,
              paddingLeft: 12,
              paddingRight: 12,
              backgroundColor: 'rgba(247, 248, 255, 1)',
            },
          }}
        />
        <TextInput
          variant="unstyled"
          label="点赞量"
          placeholder="点赞量（Optional）"
          styles={{
            label: {
              fontSize: 12,
              fontWeight: 400,
              lineHeight: '18px',
              color: labelTextColor,
            },
            wrapper: {
              marginTop: 8,
            },
            input: {
              height: 36,
              paddingLeft: 12,
              paddingRight: 12,
              backgroundColor: 'rgba(247, 248, 255, 1)',
            },
          }}
        />
        <TextInput
          variant="unstyled"
          label="IP名称"
          placeholder="IP名称（Optional）"
          styles={{
            label: {
              fontSize: 12,
              fontWeight: 400,
              lineHeight: '18px',
              color: labelTextColor,
            },
            wrapper: {
              marginTop: 8,
            },
            input: {
              height: 36,
              paddingLeft: 12,
              paddingRight: 12,
              backgroundColor: 'rgba(247, 248, 255, 1)',
            },
          }}
        />
        <TextInput
          variant="unstyled"
          label="视频时长"
          placeholder="视频时长（Optional）"
          styles={{
            label: {
              fontSize: 12,
              fontWeight: 400,
              lineHeight: '18px',
              color: labelTextColor,
            },
            wrapper: {
              marginTop: 8,
            },
            input: {
              height: 36,
              paddingLeft: 12,
              paddingRight: 12,
              backgroundColor: 'rgba(247, 248, 255, 1)',
            },
          }}
        />
      </Stack>

      <Group align="center" justify="space-between">
        <Button variant="default">清除</Button>
        <Button variant="filled" w={120} leftSection={playIcon} color="rgba(73, 81, 235, 1)">
          执行
        </Button>
      </Group>
    </Stack>
  );
};

export default Desktop;
