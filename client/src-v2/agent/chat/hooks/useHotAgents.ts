import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { searchAgents } from '~/agent/marketplace/api';
import { AgentQuery, Agent } from '~/agent/marketplace/schemas';
import { useChatContext } from '../contexts';

/**
 * 热门智能体Hook
 * 用于获取按sort_order排序的前4个热门智能体
 */
export const useHotAgents = () => {
  const [isLoading, setIsLoading] = useState(false);
  // 获取热门智能体列表
  const {
    data: hotAgents,
    isLoading: isHotAgentsLoading,
    error: hotAgentsError,
    refetch: refetchHotAgents,
  } = useQuery<Agent[]>({
    queryKey: ['hotAgents'],
    refetchOnWindowFocus: false,
    queryFn: async () => {
      setIsLoading(true);
      try {
        // 构建查询参数，按sort_order排序获取前4个智能体
        const query: AgentQuery = {
          page: 1,
          page_size: 4,
        };

        // 调用搜索API
        const result = await searchAgents(query);
        return result.data.data;
      } catch (error) {
        console.error('获取热门智能体失败:', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
  });
  const [selectedAgent, setSelectedAgent] = useState<Agent>();
  const { setAgent } = useChatContext();
  const [selectedAgentIndex, setSelectedAgentIndex] = useState<number>();
  const handleAgentCardClick = (index: number) => {
    // 更新本地状态
    setSelectedAgentIndex((prevIndex) => (prevIndex === index ? undefined : index)); // 点击已选中的卡片则取消选中，否则选中新卡片

    // 更新本地和全局状态
    const newAgent = selectedAgent?.id === hotAgents?.[index].id ? undefined : hotAgents?.[index];
    setSelectedAgent(newAgent);

    // 设置当前聊天的智能体
    setAgent(newAgent);
  };

  return {
    hotAgents,
    isLoading: isLoading || isHotAgentsLoading,
    error: hotAgentsError,
    refetch: refetchHotAgents,
    selectedAgentIndex,
    handleAgentCardClick,
    selectedAgent,
  };
};
