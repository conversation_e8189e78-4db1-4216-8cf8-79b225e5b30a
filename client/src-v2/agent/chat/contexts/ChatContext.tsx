import { createContext, useContext, useState } from 'react';
import { ChatContextType, Conversation, ConversationSchema, Message, MessageSchema } from '../schemas';
import { useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features';
import { Agent } from '~/agent/marketplace/schemas';
import { useMutation, useQuery } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { FiAlertCircle } from 'react-icons/fi';
import { contentBlocksToString, convertEventToContentBlock } from '../utils';
import { chat, stopTask } from '../api';
import { useUserStore } from '~/user/core/store';
import { getDetail } from '~/agent/marketplace/api';
import db from '../db';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';

export const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const useChatContext = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChatContext must be used within a ChatContextProvider');
  }
  return context;
};

export const ChatContextProvider = ({ children }: { children: React.ReactNode }) => {
  const mantineTheme = useMantineTheme();
  const { actualColorScheme } = useTheme(); // from ~/core/features/mantine
  const isDark = actualColorScheme === 'dark';
  const user = useUserStore((state) => state.userInfo);
  // 当前用户输入的消息
  const [inputMessage, setInputMessage] = useState<Message>();
  // 是否正在拖拽
  const [isDragging, setIsDragging] = useState(false);
  // 当前聊天的智能体
  const [agent, setAgent] = useState<Agent>();
  const [conversation, setConversation] = useState<Conversation>();
  const [taskId, setTaskId] = useState<string>();
  const [messages, setMessages] = useState<Message[]>([]);

  const { data: agentDetail } = useQuery({
    queryKey: ['agentDetail', agent?.id],
    queryFn: async () => {
      return (await getDetail(agent!.id)).data;
    },
    enabled: !!agent?.id,
    refetchOnWindowFocus: false,
  });
  const saveOrUpdateConversation = (conversation: Conversation) => {
    setConversation(conversation);
    db.conversations.put(conversation);
  };
  const saveOrUpdateMessage = (message: Message) => {
    setMessages((prevMessages) => {
      const existingIndex = prevMessages.findIndex((msg) => msg.id === message.id);
      if (existingIndex !== -1) {
        // 创建新的消息数组
        const updatedMessages = [...prevMessages];
        // 更新现有消息的内容
        updatedMessages[existingIndex] = message;
        db.messages.put(updatedMessages[existingIndex]);
        return updatedMessages;
      } else {
        db.messages.put(message);
        return [...prevMessages, message];
      }
    });
  };

  const addContentBlockToMessage = (message: Message, contentBlocks: Message['content'] = []) => {
    setMessages((prevMessages) => {
      const existingIndex = prevMessages.findIndex((msg) => msg.id === message.id);
      if (existingIndex !== -1) {
        // 创建新的消息数组
        const updatedMessages = [...prevMessages];
        // 更新现有消息的内容
        const updatedContent = [...updatedMessages[existingIndex].content, ...message.content, ...contentBlocks];
        // 创建新的消息对象
        updatedMessages[existingIndex] = {
          ...updatedMessages[existingIndex],
          content: updatedContent,
        };
        db.messages.put(updatedMessages[existingIndex]);
        return updatedMessages;
      } else {
        message.content = [...message.content, ...contentBlocks];
        db.messages.put(message);
        return [...prevMessages, message];
      }
    });
  };

  const { mutate: sendMessageMutation, isPending: isMessageSending } = useMutation({
    mutationFn: (message: Message) => {
      addContentBlockToMessage(message);

      // 创建一个带有loading内容块的临时assistant消息
      const loadingMessage = MessageSchema.parse({
        id: `loading-${Date.now()}`,
        created_at: Date.now(),
        created_at_str: dayjs(Date.now()).format('YYYY-MM-DD HH:mm:ss SSSS'),
        role: 'assistant',
        content: [
          {
            id: uuidv4(),
            type: 'loading',
          },
        ],
      });

      // 将loading消息添加到状态中，但不保存到数据库
      setMessages((prevMessages) => [...prevMessages, loadingMessage]);

      return new Promise((resolve, reject) => {
        if (user && agent) {
          chat(
            agent.id,
            {
              response_mode: 'streaming',
              conversation_id: conversation?.id ?? '',
              files: [],
              query: contentBlocksToString(message.content),
              inputs: {},
              user: user.id.toString(),
              model_config: Object.assign({}, agentDetail?.model_config, {
                appId: agent?.id,
              }),
              auto_generate_name: false,
              parent_message_id: messages.filter((msg) => msg.role === 'assistant').pop()?.id,
            },
            (event) => {
              if ('conversation_id' in event && event.conversation_id) {
                saveOrUpdateConversation(
                  ConversationSchema.parse({
                    ...conversation,
                    agent,
                    id: event.conversation_id,
                    name: conversation?.name ?? agent.name,
                    created_at: 'created_at' in event && event.created_at ? event.created_at : Date.now(),
                    updated_at: 'created_at' in event && event.created_at ? event.created_at : Date.now(),
                  }),
                );
                saveOrUpdateMessage({
                  ...message,
                  conversation_id: event.conversation_id,
                });
              }
              // 设置任务ID
              if ('task_id' in event && event.task_id) {
                setTaskId(event.task_id);
              }

              if ('message_id' in event && event.message_id) {
                // 收到第一个事件时，删除loading消息
                setMessages((prevMessages) => prevMessages.filter((msg) => !msg.id.startsWith('loading-')));

                const contentBlock = convertEventToContentBlock(event);
                if (contentBlock) {
                  const created_at = 'created_at' in event && event.created_at ? event.created_at * 1000 : Date.now();
                  const msg = MessageSchema.parse({
                    id: event.message_id,
                    conversation_id: 'conversation_id' in event && event.conversation_id ? event.conversation_id : '',
                    created_at,
                    created_at_str: dayjs(created_at).format('YYYY-MM-DD HH:mm:ss SSSS'),
                    role: 'assistant',
                    content: [contentBlock],
                  });
                  addContentBlockToMessage(msg);
                }
              }
            },
            (error) => {
              // 发生错误时也要删除loading消息
              setMessages((prevMessages) => prevMessages.filter((msg) => !msg.id.startsWith('loading-')));
              reject(error);
            },
            () => {
              resolve(void 0);
            },
          );
        } else {
          // 如果没有选择应用，也要删除loading消息
          setMessages((prevMessages) => prevMessages.filter((msg) => !msg.id.startsWith('loading-')));
          console.error('未选择应用');
        }
      });
    },
    onError: (error) => {
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '发送消息失败',
        color: 'red',
        icon: <FiAlertCircle />,
      });
    },
  });

  // 停止消息生成的mutation
  const { mutate: stopMessageMutation } = useMutation({
    mutationFn: async () => {
      if (!agent || !taskId) {
        throw new Error('无法停止消息生成：未找到智能体或任务ID');
      }

      try {
        await stopTask(agent.id, taskId);
        // 删除loading消息
        setMessages((prevMessages) => prevMessages.filter((msg) => !msg.id.startsWith('loading-')));
        notifications.show({
          title: '已停止',
          message: '已成功停止消息生成',
          color: 'blue',
        });
      } catch (error) {
        console.error('停止任务失败:', error);
        notifications.show({
          title: '错误',
          message: error instanceof Error ? error.message : '停止消息生成失败',
          color: 'red',
          icon: <FiAlertCircle />,
        });
        throw error;
      }
    },
  });

  /**
   * 获取按日期分组的会话列表
   * 将会话按照今天、昨天、7天内、更早进行分组
   */
  const getConversationsByDate = async () => {
    // 从数据库获取所有会话
    const conversations = await db.conversations.toArray();

    // 按照更新时间排序，最新的在前面
    const sortedConversations = conversations.sort((a, b) => {
      const aTime = a.updated_at || a.created_at || 0;
      const bTime = b.updated_at || b.created_at || 0;
      return bTime - aTime;
    });

    // 获取当前时间
    const now = dayjs();
    const today = now.startOf('day');
    const yesterday = today.subtract(1, 'day');
    const lastWeek = today.subtract(7, 'day');

    // 按日期分组
    const todayConversations: Conversation[] = [];
    const yesterdayConversations: Conversation[] = [];
    const lastWeekConversations: Conversation[] = [];
    const earlierConversations: Conversation[] = [];

    for (const conv of sortedConversations) {
      const convTime = dayjs(conv.updated_at || conv.created_at || 0);

      if (convTime.isAfter(today) || convTime.isSame(today, 'day')) {
        todayConversations.push(conv);
      } else if (convTime.isAfter(yesterday) || convTime.isSame(yesterday, 'day')) {
        yesterdayConversations.push(conv);
      } else if (convTime.isAfter(lastWeek) || convTime.isSame(lastWeek, 'day')) {
        lastWeekConversations.push(conv);
      } else {
        earlierConversations.push(conv);
      }
    }

    // 构建结果
    const result = [];

    if (todayConversations.length > 0) {
      result.push({
        id: '1',
        title: '今天',
        list: todayConversations,
      });
    }

    if (yesterdayConversations.length > 0) {
      result.push({
        id: '2',
        title: '昨天',
        list: yesterdayConversations,
      });
    }

    if (lastWeekConversations.length > 0) {
      result.push({
        id: '3',
        title: '7天内',
        list: lastWeekConversations,
      });
    }

    if (earlierConversations.length > 0) {
      result.push({
        id: '4',
        title: '更早',
        list: earlierConversations,
      });
    }

    return result;
  };

  /**
   * 删除会话
   * @param conversationId 会话ID
   */
  const deleteConversation = async (conversationId: string) => {
    try {
      // 删除会话
      await db.conversations.delete(conversationId);
      // 删除会话相关的所有消息
      await db.messages.where('conversation_id').equals(conversationId).delete();

      // 如果当前会话是被删除的会话，清空当前会话
      if (conversation?.id === conversationId) {
        setConversation(undefined);
      }

      return true;
    } catch (error) {
      console.error('删除会话失败:', error);
      return false;
    }
  };

  /**
   * 重命名会话
   * @param conversationId 会话ID
   * @param newName 新的会话名称
   */
  const renameConversation = async (conversationId: string, newName: string) => {
    try {
      // 从数据库获取会话
      const conv = await db.conversations.get(conversationId);
      if (!conv) {
        console.error('会话不存在:', conversationId);
        return false;
      }

      // 更新会话名称
      const updatedConv = {
        ...conv,
        name: newName,
        updated_at: Date.now(),
      };

      // 保存到数据库
      await db.conversations.put(updatedConv);

      // 如果当前会话是被重命名的会话，更新当前会话
      if (conversation?.id === conversationId) {
        setConversation(updatedConv);
      }

      return true;
    } catch (error) {
      console.error('重命名会话失败:', error);
      return false;
    }
  };

  const contextValue: ChatContextType = {
    inputBackgroundColor: isDark ? 'black' : 'white',
    borderColor: isDark ? 'rgba(51, 51, 51, 1)' : mantineTheme.colors.gray[3],
    iconColor: isDark ? 'white' : 'black',
    iconHoverColor: isDark ? 'white' : 'black', // Default, can be overridden
    inputMessage,
    setInputMessage,
    isDragging,
    setIsDragging,
    isMessageSending,
    agent,
    setAgent,
    conversation,
    setConversation,
    taskId,
    setTaskId,
    messages,
    setMessages,
    sendMessageMutation: sendMessageMutation as (message: Message) => void,
    stopMessageMutation: stopMessageMutation as () => Promise<void>,
    getConversationsByDate,
    deleteConversation,
    renameConversation,
  };

  return <ChatContext.Provider value={contextValue}>{children}</ChatContext.Provider>;
};
