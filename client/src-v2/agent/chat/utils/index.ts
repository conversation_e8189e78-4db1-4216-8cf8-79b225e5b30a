import { DocumentPreviewContentBlock, TextContentBlock, Message } from '../schemas';
import { v4 as uuidv4 } from 'uuid';
import {
  AgentMessageEvent,
  AgentThoughtEvent,
  ChatMessageEvent,
  ConversationEvent,
  ConversationEventType,
  MessageFileEvent,
} from '@dify_schemas/index';

export const contentBlocksToString = (content: Message['content']) => {
  return content
    .map((block) => {
      if (block.type === 'text') {
        return (block as TextContentBlock).content;
      }
      if (block.type === 'document_preview') {
        const docPreview = block as DocumentPreviewContentBlock;
        return `<doc-preview data-url="${docPreview.url}" > </doc-preview>`;
      }
      if (block.type === 'loading') {
        return '<loading></loading>';
      }
      return '';
    })
    .join('');
};

/**
 * 将事件转换为内容块
 * @param event SSE事件
 * @returns 内容块对象
 */
export const convertEventToContentBlock = (event: ConversationEvent): Message['content'][number] | null => {
  switch (event.event) {
    case ConversationEventType.MESSAGE: {
      // 文本消息事件
      const messageEvent = event as ChatMessageEvent;
      // 只有当answer不为空时才创建内容块
      if (!messageEvent.answer) return null;

      return {
        id: uuidv4(),
        type: 'text',
        content: messageEvent.answer,
      };
    }

    case ConversationEventType.AGENT_MESSAGE: {
      // Agent文本消息事件
      const agentMessageEvent = event as AgentMessageEvent;
      // 只有当answer不为空时才创建内容块
      if (!agentMessageEvent.answer) return null;

      return {
        id: uuidv4(),
        type: 'text',
        content: agentMessageEvent.answer,
      };
    }

    case ConversationEventType.AGENT_THOUGHT: {
      // Agent思考事件
      const agentThoughtEvent = event as AgentThoughtEvent;
      if (!agentThoughtEvent.tool) return null; // 只处理包含工具调用的思考

      return {
        id: uuidv4(),
        type: 'toolcall',
        status: 'running',
        inputs: {
          thought: agentThoughtEvent.thought,
          tool: agentThoughtEvent.tool,
          tool_input: agentThoughtEvent.tool_input,
        },
        outputs: agentThoughtEvent.observation,
      };
    }

    case ConversationEventType.MESSAGE_FILE: {
      // 文件消息事件
      const messageFileEvent = event as MessageFileEvent;

      // 根据文件类型创建不同的内容块
      if (messageFileEvent.type?.startsWith('image/')) {
        // 图片文件
        return {
          id: uuidv4(),
          type: 'image',
          src: messageFileEvent.url || '',
          name: 'image',
        };
      } else if (messageFileEvent.type?.includes('pdf')) {
        // PDF文档
        return {
          id: uuidv4(),
          type: 'document_preview',
          documentType: 'pdf',
          url: messageFileEvent.url || '',
        };
      } else if (messageFileEvent.type?.includes('word')) {
        // Word文档
        return {
          id: uuidv4(),
          type: 'document_preview',
          documentType: 'word',
          url: messageFileEvent.url || '',
        };
      } else {
        // 其他类型文件，使用文本块表示
        return {
          id: uuidv4(),
          type: 'text',
          content: `[文件: ${messageFileEvent.id || 'file'}](${messageFileEvent.url || '#'})`,
        };
      }
    }
    case ConversationEventType.MESSAGE_END:
      return null;
    default:
      throw new Error(`未处理的事件类型: ${event.event}`);
  }
};
