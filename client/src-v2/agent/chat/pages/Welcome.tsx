import React, { useEffect } from 'react';
import { Container, Group, Image, Stack, Text, Space, Skeleton } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { ossUrl } from '~/core/utils';

import { HotAgentCard, ChatBox } from '~/agent/chat/components';
import { useHotAgents } from '../hooks';
import { useChatContext } from '../contexts/ChatContext';

const Welcome = () => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';

  // 使用自定义hook获取热门智能体
  const { hotAgents, isLoading, selectedAgentIndex, handleAgentCardClick } = useHotAgents();
  const { setConversation, setMessages, setAgent } = useChatContext();

  // 进入欢迎页面时创建全新对话
  useEffect(() => {
    setConversation(undefined);
    setMessages([]);
    setAgent(undefined);
  }, [setConversation, setMessages, setAgent]);

  return (
    <Container size="912px" pt={72} pb={72}>
      <Stack align="center" gap={16}>
        <Group gap={8}>
          <Image w={68} h={39} src={ossUrl('/images/logo_icon.png')} />
          <Text fz={38} fw={700} lh="48px" c={textColor}>
            您好！我是AI应用
          </Text>
        </Group>
        <Text fz={22} fw={200} lh="36px" c={textColor}>
          作为你的智能伙伴，我既能写文案、想点子，又能陪你聊天、答疑解惑
        </Text>
      </Stack>

      <Group mt={88} gap={8}>
        {isLoading
          ? // 加载状态显示骨架屏
            Array(4)
              .fill(0)
              .map((_, index) => <Skeleton key={index} width={214} height={128} radius={14} />)
          : // 显示热门智能体卡片
            hotAgents?.map((item, index) => (
              <HotAgentCard
                key={index}
                agent={item}
                isSelected={selectedAgentIndex === index}
                onClick={() => handleAgentCardClick(index)}
              />
            ))}
      </Group>

      <Space h={56} />

      <ChatBox />
    </Container>
  );
};

export default Welcome;
Welcome.displayName = 'Welcome';
