import Dexie, { Table } from 'dexie';
import { Conversation, Message } from '../schemas';

/**
 * 聊天数据库类
 * 用于管理聊天相关的本地数据
 */
export class ChatDatabase extends <PERSON>ie {
  // 消息表
  messages!: Table<Message>;
  // 会话表
  conversations!: Table<Conversation>;

  constructor() {
    super('ChatDatabase');

    // 定义数据库结构
    this.version(1).stores({
      messages: '++id, conversation_id, created_at',
      conversations: 'id, name, created_at, updated_at',
    });
  }
}

// 创建数据库实例
export const db = new ChatDatabase();

// 导出数据库实例
export default db;

// interface ChatDatabaseService {
//   saveOrUpdateConversation: (conversation: Conversation) => Promise<void>;
//   saveOrUpdateMessage: (message: Message) => Promise<void>;
// }

// export const chatDatabaseService: ChatDatabaseService = {
//   async saveOrUpdateConversation(conversation) {
//     await db.conversations.put(conversation);
//   },
//   async saveOrUpdateMessage(message) {
//     await db.messages.put(message);
//   },
// };
