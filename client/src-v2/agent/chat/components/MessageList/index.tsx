import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface MessageListProps {}

/**
 * 消息列表组件
 * 根据设备类型自动选择桌面版或移动版
 */
const MessageList: React.FC<MessageListProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // 根据设备类型动态选择适当的组件
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default MessageList;
MessageList.displayName = 'MessageList';
