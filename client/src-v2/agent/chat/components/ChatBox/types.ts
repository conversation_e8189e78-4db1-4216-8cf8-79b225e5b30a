import { ReactNode } from 'react';

export interface ChatBoxProps {
  children?: ReactNode;
}

// 为各个子组件定义 Props 类型
export interface ChatInputTextAreaProps {
  placeholder?: string;
  rows?: number;
}

export interface ChatInputActionProps {
  // 通用操作按钮 props
  'aria-label'?: string;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface SendActionProps extends ChatInputActionProps {
  // 发送按钮特有的 props
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface ChatInputSkillsActionProps extends ChatInputActionProps {
  // 技能菜单特有的 props
}

// ... 其他子组件的 Props 定义
