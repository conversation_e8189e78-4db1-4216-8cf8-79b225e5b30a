/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';

import { Box, Group, Divider, Textarea, Menu, Button, Text } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { RiGlobalLine, RiImage2Line, RiListUnordered, RiMicLine, RiArrowDownSLine } from 'react-icons/ri';
import { FiPaperclip } from 'react-icons/fi';
import { useChatContext } from '../../contexts';

const Mobile = () => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const { isDragging } = useChatContext();

  const inputBackgroundColor = isDark ? 'black' : 'white';
  const borderColor = isDark ? 'rgba(51, 51, 51, 1)' : 'rgba(240, 242, 245, 1)';
  const iconColor = isDark ? 'rgba(163, 163, 163, 1)' : 'rgba(128, 128, 128, 1)';
  const iconHoverColor = isDark ? 'white' : 'black';

  return (
    <Box
      pt={4}
      px={32}
      pb={24}
      bg={inputBackgroundColor}
      style={{
        border: isDragging ? `2px dashed #1677ff` : `1px solid ${borderColor}`,
        borderRadius: '12px',
        boxShadow: isDragging ? '0 0 10px rgba(22, 119, 255, 0.3)' : '0px 2.05px 4.11px rgba(25, 33, 61, 0.08)',
        transition: 'all 0.2s ease',
      }}
    >
      <Group py={24} gap={24}>
        <RiGlobalLine
          className="cursor-pointer"
          size={22}
          color={iconColor}
          css={css`
            &:hover {
              color: ${iconHoverColor} !important;
            }
          `}
        />
        <RiImage2Line
          className="cursor-pointer"
          size={22}
          color={iconColor}
          css={css`
            &:hover {
              color: ${iconHoverColor} !important;
            }
          `}
        />
        <RiListUnordered
          className="cursor-pointer"
          size={22}
          color={iconColor}
          css={css`
            &:hover {
              color: ${iconHoverColor} !important;
            }
          `}
        />
        <FiPaperclip
          className="cursor-pointer"
          size={20}
          color={iconColor}
          css={css`
            &:hover {
              color: ${iconHoverColor} !important;
            }
          `}
        />
        <RiMicLine
          className="cursor-pointer"
          size={22}
          color={iconColor}
          css={css`
            &:hover {
              color: ${iconHoverColor} !important;
            }
          `}
        />
      </Group>
      <Divider />
      <Textarea
        variant="unstyled"
        mt={8}
        mb={8}
        fz={20}
        fw={500}
        lh="28px"
        rows={4}
        placeholder="通过shift+回车换行;支持复制粘贴/拖拽上传图片、文档和音频"
      />

      <Group justify="flex-end">
        <Menu shadow="md" width={200} position="right-end">
          <Group gap={0}>
            <Button
              variant="filled"
              onClick={() => console.log('发送操作')}
              color="rgba(73, 81, 235, 1)"
              style={{
                borderTopRightRadius: 0,
                borderBottomRightRadius: 0,
              }}
            >
              发送
            </Button>
            <Menu.Target>
              <Button
                variant="filled"
                color="rgba(73, 81, 235, 1)"
                px={8}
                style={{
                  borderTopLeftRadius: 0,
                  borderBottomLeftRadius: 0,
                }}
              >
                <RiArrowDownSLine size={16} />
              </Button>
            </Menu.Target>
          </Group>

          <Menu.Dropdown
            style={{
              width: 240,
              padding: '12px 16px',
              border: 0,
              borderRadius: '10px',
              boxShadow: '0px 4px 24px  rgba(0, 0, 0, 0.1)',
            }}
          >
            <Menu.Item>按 Enter 键发送</Menu.Item>
            <Menu.Item>按 Ctrl+Enter 键发送</Menu.Item>
            <Menu.Divider />
            <Menu.Item>添加一条 AI 消息</Menu.Item>
            <Menu.Item>
              <Group align="center" justify="space-between" gap={0}>
                添加一条用户消息
                <Group align="center" gap={6}>
                  <Text className="flex items-center justify-center" w={28} h={21} c="white" bg="rgba(204, 204, 204, 1)">
                    Alt
                  </Text>
                  <Text className="flex items-center justify-center" w={46} h={21} c="white" bg="rgba(204, 204, 204, 1)">
                    Enter
                  </Text>
                </Group>
              </Group>
            </Menu.Item>
          </Menu.Dropdown>
        </Menu>
      </Group>
    </Box>
  );
};

export default Mobile;
