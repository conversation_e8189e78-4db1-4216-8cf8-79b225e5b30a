import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';
import { ChatBoxProps } from './types';

// Import sub-components
import QuillTextArea from './components/QuillTextArea';
import AttachmentAction from './components/AttachmentAction';
import DeepThinkAction from './components/DeepThinkAction';
import WebSearchAction from './components/WebSearchAction';
import SkillsAction from './components/SkillsAction';
import ImageAction from './components/ImageAction';
import MicAction from './components/MicAction';
import SendAction from './components/SendAction';
import StopAction from './components/StopAction';

const ChatBox: React.FC<ChatBoxProps> & {
  QuillTextArea: typeof QuillTextArea;
  AttachmentAction: typeof AttachmentAction;
  DeepThinkAction: typeof DeepThinkAction;
  WebSearchAction: typeof WebSearchAction;
  SkillsAction: typeof SkillsAction;
  ImageAction: typeof ImageAction;
  MicAction: typeof MicAction;
  SendAction: typeof SendAction;
  StopAction: typeof StopAction;
} = ({ children }) => {
  const { isMobile } = useDeviceDetect();
  return children ? children : isMobile ? <Mobile /> : <Desktop />;
};

// Assign sub-components as static properties
ChatBox.QuillTextArea = QuillTextArea;
ChatBox.AttachmentAction = AttachmentAction;
ChatBox.DeepThinkAction = DeepThinkAction;
ChatBox.WebSearchAction = WebSearchAction;
ChatBox.SkillsAction = SkillsAction;
ChatBox.ImageAction = ImageAction;
ChatBox.MicAction = MicAction;
ChatBox.SendAction = SendAction;
ChatBox.StopAction = StopAction;

ChatBox.displayName = 'ChatBox';
export default ChatBox;
