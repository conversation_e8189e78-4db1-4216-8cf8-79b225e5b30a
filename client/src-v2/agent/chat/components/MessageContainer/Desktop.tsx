import React from 'react';
import { Box, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { MessageContainerProps } from '.';

const Desktop: React.FC<MessageContainerProps> = ({ children, data }) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  // 根据角色设置样式
  const isUser = data?.role === 'user';
  const userMsgBgColor = isDark ? theme.colors.dark[6] : theme.colors.gray[2];

  return (
    <Box
      className="w-full flex"
      style={{
        justifyContent: isUser ? 'flex-end' : 'flex-start',
      }}
    >
      <Box
        p={16}
        style={{
          background: isUser ? userMsgBgColor : 'transparent',
          borderRadius: isUser ? '12px' : undefined,
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default Desktop;
