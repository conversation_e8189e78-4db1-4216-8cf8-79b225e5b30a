/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import { Stack, Image, Text, Group } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { motion } from 'motion/react'; // 导入 Motion One for React

import { HotAgentCardProps } from './types';

// SVG Check Icon Component
const CheckIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="red"
    strokeWidth="3"
    strokeLinecap="round"
    strokeLinejoin="round"
    style={{
      position: 'absolute',
      top: '10px',
      right: '10px',
      zIndex: 1,
    }}
  >
    <polyline points="20 6 9 17 4 12"></polyline>
  </svg>
);

const Mobile: React.FC<HotAgentCardProps> = ({ agent, onClick, isSelected }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const itemBgColor = isDark ? 'black' : 'white';
  const itemHoverBgColor = isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(255, 255, 255, 0.5)';
  const itemBorderColor = isDark ? 'rgba(26, 24, 20, 1)' : 'rgba(229, 231, 235, 1)';
  // 移除 itemSelectedBorderColor，因为选中状态仅由打勾图标指示
  const itemTitleColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const itemTextColor = isDark ? 'rgba(124, 124, 124, 1)' : 'rgba(131, 131, 131, 1)';

  return (
    <motion.div // 使用 motion.div 作为根元素
      className="cursor-pointer"
      onClick={onClick}
      whileTap={{ scale: 0.97 }} // 点击时的动画效果
      style={{
        // 将 Stack 的样式和 props 移到这里
        width: 214,
        height: 140,
        paddingLeft: 12,
        paddingRight: 12,
        paddingTop: 14,
        paddingBottom: 14,
        background: itemBgColor,
        borderRadius: '14px',
        border: `1px solid ${itemBorderColor}`, // 边框颜色不因选中状态而改变
        position: 'relative', // 为了绝对定位打勾图标
        display: 'flex', // 模拟 Stack 的 flex 布局
        flexDirection: 'column', // 模拟 Stack 的 flex 布局
        gap: 0, // 模拟 Stack 的 gap
      }}
      css={css`
        &:hover {
          background: ${itemHoverBgColor} !important;
        }
      `}
    >
      {isSelected && <CheckIcon />} {/* 条件渲染打勾图标 */}
      <Stack gap={0} style={{ flexGrow: 1 }}>
        {' '}
        {/* 内部仍然可以使用 Stack 来组织内容 */}
        <Group gap={8} wrap="nowrap">
          <Image w={32} h={32} src={agent.icon} />
          <Text fz={16} fw={500} lh="30px" c={itemTitleColor}>
            {agent.name}
          </Text>
        </Group>
        <Text
          fz={16} // 注意：Desktop版此处为14，Mobile版此处为16，保持原样
          fw={400}
          lh="20px"
          c={itemTextColor}
          mt={8}
          style={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2, // Mobile版为2行
            WebkitBoxOrient: 'vertical',
            maxHeight: '40px',
          }}
        >
          {agent.description}
        </Text>
      </Stack>
    </motion.div>
  );
};

export default Mobile;
