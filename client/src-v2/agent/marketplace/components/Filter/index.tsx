import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

interface FilterTabsProps {
  /**
   * Array of category names to display as tabs
   */
  categories: string[];
  /**
   * Currently selected category
   */
  selectedCategory: string;
  /**
   * Callback function when a category is selected
   */
  onCategoryChange: (category: string) => void;
}

/**
 * Responsive FilterTabs component that loads Desktop or Mobile version
 * based on the device type
 */
const FilterTabs: React.FC<FilterTabsProps> = props => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default FilterTabs;
