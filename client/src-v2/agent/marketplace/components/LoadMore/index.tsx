import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

export interface LoadMoreProps {
  /**
   * 按钮文本
   * @default "查看更多"
   */
  text?: string;
  /**
   * 点击事件处理函数
   */
  onClick?: () => void;
  /**
   * 是否禁用按钮
   * @default false
   */
  disabled?: boolean;
  /**
   * 是否显示加载状态
   * @default false
   */
  loading?: boolean;
  /**
   * 自定义类名
   */
  className?: string;
}

/**
 * 响应式 LoadMore 按钮组件，根据设备类型自动选择桌面版或移动版
 */
const LoadMore: React.FC<LoadMoreProps> = props => {
  const { isMobile } = useDeviceDetect();

  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default LoadMore;
