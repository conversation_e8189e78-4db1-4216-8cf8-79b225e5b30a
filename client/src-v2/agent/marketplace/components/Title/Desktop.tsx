import React from 'react';
import { useTheme } from '~/core/features/mantine/theme-context';

interface DesktopAgentTitleProps {
  /**
   * Main title text
   */
  title: string;
  /**
   * Subtitle text
   */
  subtitle: string;
}

/**
 * Desktop version of the AgentTitle component
 */
const DesktopAgentTitle: React.FC<DesktopAgentTitleProps> = ({
  title,
  subtitle
}) => {
  const { actualColorScheme } = useTheme();

  // Determine text colors based on theme
  const titleColor = actualColorScheme === 'dark' ? '#8C9EFF' : '#5668FF';
  const subtitleColor = actualColorScheme === 'dark' ? '#C1C2C5' : '#404040';

  return (
    <div className="flex flex-col gap-4 items-center text-center">
      <div
        className="text-4xl font-bold font-['Source_Han_Sans_SC'] leading-[48.39px]"
        style={{ color: titleColor }}
      >
        {title}
      </div>
      <div
        className="text-xl font-['Source_Han_Sans_SC'] leading-9 max-w-[800px]"
        style={{ color: subtitleColor }}
      >
        {subtitle}
      </div>
    </div>
  );
};

export default DesktopAgentTitle;
