import React, { ReactNode } from 'react';
import './styles.css';

interface DesktopAgentListProps {
  /**
   * Children components (AgentCard components)
   */
  children: ReactNode;
  /**
   * Number of columns in the grid
   * @default 3
   */
  columns?: number;
  /**
   * Gap between grid items
   * @default "md"
   */
  gap?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

/**
 * Desktop version of the AgentList component
 */
const DesktopAgentList: React.FC<DesktopAgentListProps> = ({
  children,
  columns = 3,
  gap = 'md'
}) => {
  // Map gap size to pixel values
  const gapSizeMap = {
    xs: '0.5rem',
    sm: '1rem',
    md: '1.5rem',
    lg: '2rem',
    xl: '2.5rem'
  };

  return (
    <div
      className="agent-list-grid"
      style={{
        display: 'grid',
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap: gapSizeMap[gap]
      }}
    >
      {children}
    </div>
  );
};

export default DesktopAgentList;
