import { useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { createOrder } from '~/user/api';
import { getPaymentPlans } from '~/paymentPlan/api';
import { OrderCreateRequest, OrderResult, PaymentMethod } from '~/user/schemas';
import { PaymentPlan } from '~/paymentPlan/schemas';
import { AgentDetail } from '../schemas';
import { ResponsePayloads } from '~/core/schemas';

/**
 * 智能体支付Hook
 * 用于处理智能体购买支付流程
 */
export const useAgentPayment = (agentId?: string) => {
  // 支付方式
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('alipay');
  // 微信支付二维码URL
  const [wechatPaymentUrl, setWechatPaymentUrl] = useState<string>('');
  // 订单ID
  const [orderId, setOrderId] = useState<string>('');
  // 支付弹窗状态
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  // 选中的付费计划ID
  const [selectedPlanId, setSelectedPlanId] = useState<number | undefined>(undefined);

  // 获取付费计划列表
  const { data: paymentPlansResponse, isLoading: isPaymentPlansLoading } = useQuery({
    queryKey: ['paymentPlans', 'app', agentId],
    queryFn: () => getPaymentPlans('app', agentId!),
    enabled: !!agentId,
  });

  // 提取付费计划数据
  const paymentPlans = paymentPlansResponse?.data || [];

  // 获取选中的付费计划
  const selectedPlan = paymentPlans.find((plan) => plan.id === selectedPlanId);

  // 处理选择付费计划
  const handleSelectPlan = (plan: PaymentPlan) => {
    setSelectedPlanId(plan.id);
  };

  // 支付处理逻辑
  const handlePayment = useMutation<ResponsePayloads<OrderResult>, Error, OrderCreateRequest>({
    mutationFn: createOrder,
    onSuccess: (response) => {
      if (response.data) {
        notifications.show({
          title: '提示',
          message: '订单已创建，正在跳转...',
          color: 'green',
        });

        setOrderId(response.data.order_id);

        if (response.data?.payment_url) {
          if (paymentMethod === 'wechatpay') {
            setWechatPaymentUrl(response.data.payment_url);
          } else {
            window.open(response.data.payment_url, '_blank');
          }
        }
      } else {
        notifications.show({
          title: '支付失败',
          message: response.error?.message || '未知错误',
          color: 'red',
        });
      }
    },
    onError: (error) => {
      notifications.show({
        title: '请求失败',
        message: error.message,
        color: 'red',
      });
    },
  });

  // 处理智能体购买
  const handleAgentPurchase = (agent: AgentDetail) => {
    if (!paymentMethod) {
      notifications.show({
        title: '请选择支付方式',
        message: '请先选择支付宝或微信支付',
        color: 'yellow',
      });
      return;
    }

    if (!selectedPlanId) {
      notifications.show({
        title: '请选择付费计划',
        message: '请先选择一个付费计划',
        color: 'yellow',
      });
      return;
    }

    // 创建订单请求
    handlePayment.mutate({
      product_type: 'app',
      product_id: agent.id,
      payment_plan_id: selectedPlanId,
      quantity: 1,
      payment_method: paymentMethod,
    });
  };

  // 打开支付弹窗
  const openPaymentModal = () => {
    setPaymentModalOpen(true);
  };

  // 关闭支付弹窗
  const closePaymentModal = () => {
    setPaymentModalOpen(false);
    setWechatPaymentUrl('');
  };

  return {
    // 状态
    paymentMethod,
    wechatPaymentUrl,
    orderId,
    paymentModalOpen,
    isPaymentLoading: handlePayment.isPending,
    paymentPlans,
    selectedPlanId,
    selectedPlan,
    isPaymentPlansLoading,

    // 操作方法
    setPaymentMethod,
    handleAgentPurchase,
    openPaymentModal,
    closePaymentModal,
    handleSelectPlan,
    setSelectedPlanId,
  };
};

export default useAgentPayment;
