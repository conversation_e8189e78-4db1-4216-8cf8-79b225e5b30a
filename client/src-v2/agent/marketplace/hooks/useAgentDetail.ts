import { useState, useCallback, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getDetail } from '../api';
import { AgentDetail } from '../schemas';

/**
 * 智能体详情Hook
 * 用于管理智能体详情模态框的状态和数据
 */
export const useAgentDetail = () => {
  // 状态管理：控制详情模态框
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedAgentId, setSelectedAgentId] = useState<string>('');

  // 获取智能体详情数据
  const {
    data: agentDetailResponse,
    isLoading: isAgentDetailLoading,
    error: agentDetailError,
  } = useQuery({
    queryKey: ['agentDetail', selectedAgentId],
    queryFn: () => getDetail(selectedAgentId),
    enabled: isModalOpen && !!selectedAgentId,
    refetchOnWindowFocus: false,
  });

  // 提取智能体详情数据
  const agentDetail = agentDetailResponse?.data as AgentDetail;

  // 处理打开详情模态框
  const handleOpenModal = useCallback((agentId: string) => {
    setSelectedAgentId(agentId);
    setIsModalOpen(true);
  }, []);

  // 处理关闭详情模态框
  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
  }, []);

  // 监听自定义事件，用于从其他组件打开智能体详情
  useEffect(() => {
    // 定义事件处理函数
    const handleOpenAgentDetailEvent = (event: CustomEvent<{ agentId: string }>) => {
      if (event.detail && event.detail.agentId) {
        handleOpenModal(event.detail.agentId);
      }
    };

    // 添加事件监听器
    window.addEventListener('openAgentDetail', handleOpenAgentDetailEvent as EventListener);

    // 组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('openAgentDetail', handleOpenAgentDetailEvent as EventListener);
    };
  }, [handleOpenModal]);

  return {
    // 状态
    isModalOpen,
    selectedAgentId,
    agentDetail,
    isAgentDetailLoading,
    agentDetailError,

    // 操作方法
    handleOpenModal,
    handleCloseModal,
  };
};

export default useAgentDetail;
