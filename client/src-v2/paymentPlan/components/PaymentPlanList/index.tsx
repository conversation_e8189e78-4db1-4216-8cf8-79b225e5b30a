import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';
import { PaymentPlanListProps } from './types';

/**
 * 付费计划列表组件
 * 根据设备类型自动选择桌面版或移动版
 */
const PaymentPlanList: React.FC<PaymentPlanListProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // 根据设备类型动态选择适当的组件
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default PaymentPlanList;
