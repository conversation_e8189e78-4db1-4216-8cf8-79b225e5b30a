import React from 'react';
import { Card, Group, Stack, Text, Badge, useMantineTheme } from '@mantine/core';
import { PaymentPlan } from '../../schemas';

interface MobilePaymentPlanCardProps {
  /**
   * 付费计划数据
   */
  plan: PaymentPlan;
  /**
   * 是否选中
   */
  isSelected?: boolean;
  /**
   * 点击事件
   */
  onClick?: (plan: PaymentPlan) => void;
  /**
   * 是否为暗色主题
   */
  isDark?: boolean;
}

/**
 * 付费计划卡片组件 - 移动版
 */
const Mobile: React.FC<MobilePaymentPlanCardProps> = ({ plan, isSelected = false, onClick, isDark = false }) => {
  const theme = useMantineTheme();

  // 处理点击事件
  const handleClick = () => {
    if (onClick) {
      onClick(plan);
    }
  };

  // 计算有效期显示文本
  const validityText = plan.validity_period > 0 ? `${plan.validity_period}天` : '永久';

  return (
    <Card
      shadow="sm"
      padding="md"
      radius="md"
      withBorder
      style={{
        width: '240px',
        height: '160px',
        cursor: 'pointer',
        borderColor: isSelected ? theme.colors.blue[6] : undefined,
        backgroundColor: isSelected ? (isDark ? theme.colors.dark[5] : theme.colors.gray[0]) : undefined,
        transition: 'all 0.2s ease',
      }}
      onClick={handleClick}
    >
      <Group position="apart" wrap="nowrap">
        <Stack gap="xs" style={{ flex: 1 }}>
          <Group position="apart" align="center">
            <Text fw={700} size="md" color={isSelected ? theme.colors.blue[6] : undefined}>
              {plan.name}
            </Text>
            {isSelected && (
              <Badge color="blue" variant="filled" size="xs">
                已选择
              </Badge>
            )}
          </Group>

          {plan.description && (
            <Text size="xs" color="dimmed" lineClamp={1}>
              {plan.description}
            </Text>
          )}
        </Stack>

        <Stack gap={0} align="flex-end">
          <Group gap={2} align="flex-end">
            <Text size="xs" color={isDark ? theme.colors.gray[5] : theme.colors.gray[6]}>
              ¥
            </Text>
            <Text size="lg" fw={700} color={isDark ? theme.colors.red[4] : theme.colors.red[6]}>
              {plan.price.toFixed(2)}
            </Text>
          </Group>
          {plan.original_price > 0 && plan.original_price > plan.price && (
            <Text size="xs" color="dimmed" style={{ textDecoration: 'line-through' }}>
              ¥{plan.original_price.toFixed(2)}
            </Text>
          )}
          <Badge color="green" variant="light" size="xs" mt={4}>
            {validityText}
          </Badge>
        </Stack>
      </Group>
    </Card>
  );
};

export default Mobile;
