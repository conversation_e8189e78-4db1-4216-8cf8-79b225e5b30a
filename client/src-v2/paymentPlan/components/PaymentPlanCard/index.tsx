import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import { PaymentPlan } from '../../schemas';
import Desktop from './Desktop';
import Mobile from './Mobile';

interface PaymentPlanCardProps {
  /**
   * 付费计划数据
   */
  plan: PaymentPlan;
  /**
   * 是否选中
   */
  isSelected?: boolean;
  /**
   * 点击事件
   */
  onClick?: (plan: PaymentPlan) => void;
  /**
   * 是否为暗色主题
   */
  isDark?: boolean;
}

/**
 * 付费计划卡片组件
 * 根据设备类型自动选择桌面版或移动版
 */
const PaymentPlanCard: React.FC<PaymentPlanCardProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // 根据设备类型动态选择适当的组件
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default PaymentPlanCard;
